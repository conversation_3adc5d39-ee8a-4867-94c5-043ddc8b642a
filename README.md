# tagTok - TikTok Video Organization Platform

A comprehensive fullstack web application for uploading, transcribing, and organizing TikTok videos with AI-powered tagging, analytics, and multi-user collaboration.

## 🚀 Quick Deployment

Deploy your own tagTok instance in minutes:

```bash
# Clone the repository
git clone https://github.com/yourusername/tagtok-multi-users.git
cd tagtok-multi-users

# Run the deployment script
./deploy.sh
```

The script will guide you through configuration and automatically deploy your instance. For manual deployment or advanced configuration, see [DEPLOYMENT.md](DEPLOYMENT.md).

## 🆕 Multi-User System

TagTok now supports multi-user authentication and video sharing! See [MULTIUSER_SETUP.md](MULTIUSER_SETUP.md) for migration instructions.

### New Features:
- 🔐 **User Authentication**: JWT-based login system with secure password hashing
- 👥 **Video Sharing**: Share videos with other users with granular permissions
- 🔒 **Permission System**: Four levels - View, Download, Comment, Owner
- 🎯 **Enhanced Recipe Extraction**: Improved Spanish keyword detection for cooking videos

## 🌟 Features

### Core Functionality
- 📹 **Multi-Video Upload**: Drag & drop interface for uploading multiple TikTok videos
- 🎯 **AI-Powered Transcription**: Automatic transcription using OpenAI Whisper (CPU-only)
- 🏷️ **Smart Tagging**: AI-generated tags using spaCy and KeyBERT for content analysis
- 🔍 **Advanced Search**: Search by tags, transcript content, filename, or metadata
- 📊 **Analytics Dashboard**: Comprehensive insights into your video library
- 📱 **Mobile-Responsive**: Optimized for desktop, tablet, and mobile devices

### Video Management
- 🎬 **Video Player**: Built-in video player with controls and thumbnails
- ✏️ **Metadata Editing**: Edit video titles, descriptions, and custom tags
- 🗂️ **Tag Organization**: Create, edit, and manage colored tags with descriptions
- 📥 **Download**: Download original video files
- 🗑️ **Smart Deletion**: Delete videos and automatically clean up unused tags

### Data & Analytics
- 📈 **Usage Statistics**: Track upload trends, tag popularity, and processing status
- 🌍 **Language Analytics**: Automatic language detection and distribution charts
- ⏱️ **Duration Analysis**: Video length distribution and statistics
- 📤 **Export Options**: Export metadata as CSV or JSON files
- 💾 **Local Storage**: All data stored locally with no cloud dependencies

## 🛠️ Tech Stack

### Backend
- **Framework**: Python FastAPI with async support
- **Database**: SQLite with SQLAlchemy ORM
- **AI/ML**: OpenAI Whisper, spaCy, KeyBERT, sentence-transformers
- **Video Processing**: OpenCV, MoviePy, FFmpeg
- **API**: RESTful API with automatic OpenAPI documentation

### Frontend
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with responsive design
- **State Management**: TanStack Query (React Query) for server state
- **UI Components**: Headless UI, Heroicons
- **Video Player**: Video.js with custom styling
- **File Upload**: React Dropzone with drag & drop

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Reverse Proxy**: Nginx for routing and static file serving
- **Development**: Hot reload for both frontend and backend
- **Production**: Optimized builds with health checks

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM (recommended for AI processing)
- 10GB+ free disk space for videos and models

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   ```

2. **Start the application**:
   ```bash
   docker-compose up --build
   ```

3. **Access the application**:
   - **Main App**: http://localhost (or http://localhost:80)
   - **Frontend**: http://localhost:3000 (direct access)
   - **Backend API**: http://localhost:8000
   - **API Documentation**: http://localhost:8000/docs

### First Steps
1. Navigate to the Upload page
2. Drag and drop your TikTok videos
3. Wait for processing (transcription + tagging)
4. Explore your videos in the gallery
5. Check out the analytics dashboard

## 🌐 Deployment Guide

### Local Network Deployment

Deploy tagTok on your local network to access from multiple devices (phones, tablets, other computers).

#### Step 1: Prepare the Host Computer

1. **Install Docker and Docker Compose**:
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install docker.io docker-compose
   sudo usermod -aG docker $USER

   # macOS (using Homebrew)
   brew install docker docker-compose

   # Windows: Download Docker Desktop from docker.com
   ```

2. **Clone and Setup**:
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2

   # Create data directories
   mkdir -p data/{videos,db,transcripts}
   chmod -R 755 data/
   ```

#### Step 2: Configure for Network Access

1. **Find your computer's IP address**:
   ```bash
   # Linux/macOS
   ip addr show | grep "inet " | grep -v 127.0.0.1
   # or
   ifconfig | grep "inet " | grep -v 127.0.0.1

   # Windows
   ipconfig | findstr "IPv4"
   ```

   Example output: `*************` (your IP will be different)

2. **Update Docker Compose configuration** (optional - for custom ports):
   ```yaml
   # In docker-compose.yml, you can change ports if needed:
   services:
     nginx:
       ports:
         - "8790:80"    # Change first number for different port
     frontend:
       ports:
         - "3001:3000"  # Direct frontend access
     backend:
       ports:
         - "8090:8000"  # Direct API access
     ollama:
       ports:
         - "11435:11434" # Ollama AI service
   ```

#### Step 3: Deploy the Application

1. **Start the application**:
   ```bash
   docker-compose up -d --build
   ```

2. **Verify deployment**:
   ```bash
   # Check all services are running
   docker-compose ps

   # Test local access
   curl http://localhost:8790/api/health

   # Test network access (replace with your IP)
   curl http://*************:8790/api/health
   ```

#### Step 4: Access from Other Devices

1. **From any device on the same network**:
   - **Main Application**: `http://*************` (replace with your IP)
   - **Direct Frontend**: `http://*************:3000`
   - **API Documentation**: `http://*************:8000/docs`

2. **Mobile Access**:
   - Open Safari/Chrome on your phone
   - Navigate to `http://*************`
   - The interface is fully mobile-responsive

### Remote Server Deployment

Deploy tagTok on a remote server (VPS, cloud instance, etc.) for internet access.

#### Prerequisites
- Ubuntu/Debian server with root access
- Domain name (optional but recommended)
- SSL certificate (for HTTPS - optional)

#### Step 1: Server Setup

1. **Update system and install Docker**:
   ```bash
   sudo apt update && sudo apt upgrade -y
   sudo apt install docker.io docker-compose git -y
   sudo usermod -aG docker $USER
   sudo systemctl enable docker
   sudo systemctl start docker
   ```

2. **Configure firewall**:
   ```bash
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS (if using SSL)
   sudo ufw enable
   ```

#### Step 2: Deploy Application

1. **Clone and setup**:
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   mkdir -p data/{videos,db,transcripts}
   chmod -R 755 data/
   ```

2. **Configure for production**:
   ```bash
   # Edit docker-compose.yml for production
   # Remove development volumes and enable restart policies
   ```

3. **Start services**:
   ```bash
   docker-compose up -d --build
   ```

#### Step 3: Domain and SSL (Optional)

1. **Configure domain DNS**:
   - Point your domain A record to your server IP
   - Wait for DNS propagation (up to 24 hours)

2. **Setup SSL with Let's Encrypt**:
   ```bash
   # Install certbot
   sudo apt install certbot python3-certbot-nginx

   # Get SSL certificate
   sudo certbot --nginx -d yourdomain.com
   ```

### Production Considerations

#### Security
```bash
# Change default ports (in docker-compose.yml)
services:
  nginx:
    ports:
      - "8080:80"  # Use non-standard port

# Disable direct backend/frontend access
# Comment out these port mappings:
# backend:
#   ports:
#     - "8000:8000"
# frontend:
#   ports:
#     - "3000:3000"
```

#### Performance Optimization
```yaml
# In docker-compose.yml, add resource limits:
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
```

#### Backup Strategy
```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "tagTok_backup_$DATE.tar.gz" data/
# Upload to cloud storage or external backup location
```

#### Monitoring
```bash
# Check application health
curl http://localhost/api/health

# Monitor resource usage
docker stats

# View logs
docker-compose logs -f --tail=100
```

### Troubleshooting Network Access

#### Common Issues

1. **Cannot access from other devices**:
   ```bash
   # Check if services are bound to all interfaces
   netstat -tlnp | grep :80

   # Ensure firewall allows connections
   sudo ufw status

   # Test from host machine
   curl http://localhost/api/health
   curl http://YOUR_IP/api/health
   ```

2. **Mobile devices show "Failed to load videos"**:
   - This is automatically handled by the dynamic API detection
   - The app detects IP access and uses the correct API endpoint
   - Check browser console for detailed error messages

3. **Slow performance on mobile**:
   ```bash
   # Reduce Whisper model size for faster processing
   # Edit backend/utils/ai_utils.py:
   self.model_size = "tiny"  # Instead of "base"

   # Restart backend
   docker-compose restart backend
   ```

#### Network Diagnostics
```bash
# Test connectivity from another device
ping YOUR_IP

# Check if ports are accessible
telnet YOUR_IP 80
telnet YOUR_IP 8000

# Verify Docker networking
docker network ls
docker network inspect tagTok-v2_tagTok-network
```

## 📁 Project Structure

```
tagTok-v2/
├── backend/                    # FastAPI backend application
│   ├── main.py                # Application entry point
│   ├── models/                # Database models and schemas
│   │   ├── database.py        # SQLAlchemy models
│   │   └── schemas.py         # Pydantic schemas
│   ├── routers/               # API route handlers
│   │   ├── videos.py          # Video management endpoints
│   │   ├── tags.py            # Tag management endpoints
│   │   ├── analytics.py       # Analytics endpoints
│   │   └── export.py          # Data export endpoints
│   ├── services/              # Business logic layer
│   │   ├── video_service.py   # Video operations
│   │   ├── tag_service.py     # Tag operations
│   │   ├── analytics_service.py # Analytics calculations
│   │   ├── export_service.py  # Data export logic
│   │   └── processing_service.py # AI processing pipeline
│   ├── utils/                 # Utility functions
│   │   ├── video_utils.py     # Video processing utilities
│   │   └── ai_utils.py        # AI/ML utilities
│   ├── requirements.txt       # Python dependencies
│   └── Dockerfile            # Backend container config
├── frontend/                  # React frontend application
│   ├── src/
│   │   ├── components/        # Reusable React components
│   │   │   ├── Layout.tsx     # Main layout component
│   │   │   ├── VideoGrid.tsx  # Video gallery grid
│   │   │   ├── VideoCard.tsx  # Individual video cards
│   │   │   ├── VideoPlayer.tsx # Video player component
│   │   │   ├── TagManager.tsx # Tag management interface
│   │   │   ├── SearchBar.tsx  # Search functionality
│   │   │   ├── FilterPanel.tsx # Advanced filtering
│   │   │   └── ...           # Other components
│   │   ├── pages/            # Page components
│   │   │   ├── HomePage.tsx   # Video gallery page
│   │   │   ├── VideoDetailPage.tsx # Video detail view
│   │   │   ├── UploadPage.tsx # Upload interface
│   │   │   └── AnalyticsPage.tsx # Analytics dashboard
│   │   ├── utils/            # Utility functions
│   │   │   └── api.ts        # API client functions
│   │   ├── types/            # TypeScript type definitions
│   │   └── hooks/            # Custom React hooks
│   ├── package.json          # Node.js dependencies
│   └── Dockerfile           # Frontend container config
├── data/                     # Persistent data (created automatically)
│   ├── videos/              # Uploaded video files
│   ├── db/                  # SQLite database
│   └── transcripts/         # Transcript files
├── docker-compose.yml       # Multi-container orchestration
├── nginx.conf              # Nginx reverse proxy config
└── README.md              # This file
```

## 🔧 Development

### Local Development Setup

#### Backend Development
```bash
# Navigate to backend directory
cd backend

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn main:app --reload --host 0.0.0.0 --port 8090
```

#### Frontend Development
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

#### Full Stack Development
```bash
# Run both backend and frontend with hot reload
docker-compose up --build

# Or run in detached mode
docker-compose up -d --build
```

### Development Tools

#### API Documentation
- **Swagger UI**: http://localhost:8090/docs
- **ReDoc**: http://localhost:8090/redoc

#### Database Management
```bash
# Access SQLite database directly
sqlite3 data/db/tagTok.db

# View database schema
.schema

# List all tables
.tables
```

#### Debugging
```bash
# View real-time logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend bash
```

## 📡 API Reference

### Video Endpoints
- `GET /videos` - List videos with filtering and pagination
  - Query params: `skip`, `limit`, `tags`, `search`, `language`, `processed`
- `POST /videos/upload` - Upload multiple video files
- `GET /videos/{id}` - Get video details by ID
- `PUT /videos/{id}` - Update video metadata
- `DELETE /videos/{id}` - Delete video and associated files
- `GET /videos/{id}/transcript` - Get video transcript
- `POST /videos/{id}/reprocess` - Reprocess video (transcription + tagging)
- `GET /videos/{id}/download` - Download original video file

### Tag Endpoints
- `GET /tags` - List all tags with sorting and filtering
- `POST /tags` - Create new tag
- `GET /tags/{id}` - Get tag details
- `PUT /tags/{id}` - Update tag
- `DELETE /tags/{id}` - Delete tag
- `POST /tags/{tag_id}/videos/{video_id}` - Add tag to video
- `DELETE /tags/{tag_id}/videos/{video_id}` - Remove tag from video
- `GET /tags/cloud/data` - Get tag cloud visualization data

### Analytics Endpoints
- `GET /analytics` - Get comprehensive analytics
- `GET /analytics/summary` - Get basic statistics summary
- `GET /analytics/tags/top` - Get top tags by usage
- `GET /analytics/videos/timeline` - Get upload timeline
- `GET /analytics/videos/duration-distribution` - Get duration distribution
- `GET /analytics/languages` - Get language distribution
- `GET /analytics/processing-status` - Get processing status breakdown

### Export Endpoints
- `GET /export/videos` - Export video metadata (CSV/JSON)
- `GET /export/tags` - Export tag data (CSV/JSON)
- `GET /export/analytics` - Export analytics data (JSON)

### Utility Endpoints
- `GET /health` - Health check endpoint
- `GET /videos/file/{filename}` - Serve video files
- `GET /videos/thumbnail/{filename}` - Serve thumbnail images

## ⚙️ Configuration

tagTok is designed to be easily configurable for different deployment scenarios. All configuration is managed through environment variables in the `.env` file.

### Quick Configuration

For most deployments, you only need to change these key variables:

```bash
# Your domain (for production deployment)
PUBLIC_DOMAIN=yourdomain.com

# Deployment mode: development, staging, production
DEPLOYMENT_MODE=production

# Generate with: openssl rand -hex 32
SECRET_KEY=your-secure-secret-key

# Application branding
APP_NAME=MyTagTok
APP_DESCRIPTION=My video organization platform

# Feature flags
ENABLE_REGISTRATION=true
ENABLE_PUBLIC_SHARING=true
```

### Auto-Configuration

tagTok automatically configures many settings based on your `PUBLIC_DOMAIN` and `DEPLOYMENT_MODE`:

- **CORS Origins**: Automatically allows your domain and localhost for development
- **API URLs**: Frontend automatically uses the correct API endpoint
- **SSL/HTTPS**: Automatically detects and configures for production domains

### Manual Configuration

For advanced setups, you can override the auto-configuration:

```bash
# Manually set CORS origins
CORS_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com

# Manually set API URL for frontend
REACT_APP_API_URL=https://api.yourdomain.com

# Database configuration (SQLite default, PostgreSQL for production)
DATABASE_URL=postgresql://user:pass@host:port/database
```

### Deployment Modes

| Mode | Description | Auto-Configuration |
|------|-------------|-------------------|
| `development` | Local development with hot reload | Permissive CORS, localhost URLs |
| `staging` | Production-like testing | Mixed localhost + domain URLs |
| `production` | Live deployment | Strict CORS, HTTPS URLs |

For complete configuration options, see [DEPLOYMENT.md](DEPLOYMENT.md).

### Docker Compose Configuration

#### Port Mapping
```yaml
services:
  nginx:
    ports:
      - "8790:80"      # Main application
  backend:
    ports:
      - "8090:8000"    # Backend API
  frontend:
    ports:
      - "3001:3000"    # Frontend dev server
  ollama:
    ports:
      - "11435:11434"  # Ollama AI service
```

#### Volume Mapping
```yaml
volumes:
  - ./data/videos:/app/videos      # Video storage
  - ./data/db:/app/db              # Database storage
  - ./data/transcripts:/app/transcripts  # Transcript storage
```

### AI Model Configuration

#### Whisper Model Sizes
- `tiny`: Fastest, least accurate (~39 MB)
- `base`: Good balance (~74 MB) - **Default**
- `small`: Better accuracy (~244 MB)
- `medium`: High accuracy (~769 MB)
- `large`: Best accuracy (~1550 MB)

Change model size in `backend/utils/ai_utils.py`:
```python
self.model_size = "base"  # Change to desired size
```

### Video Download Authentication

Some videos (especially from TikTok) may require authentication to download. If you encounter errors like "This post may not be comfortable for some audiences. Log in for access", you need to configure cookies.

#### Method 1: Using Browser Cookies (Recommended)
Set the browser name to extract cookies from:
```bash
# In your .env file
YTDLP_COOKIES_FROM_BROWSER=chrome
# Supported browsers: chrome, firefox, safari, edge, opera, brave
```

#### Method 2: Using Cookies File
1. Export cookies from your browser using a browser extension
2. Save the cookies file to `./data/cookies/cookies.txt`
3. Configure the path:
```bash
# In your .env file
YTDLP_COOKIES_FILE=/app/cookies/cookies.txt
```

#### Browser Cookie Export Instructions
- **Chrome**: Use "Get cookies.txt LOCALLY" extension
- **Firefox**: Use "cookies.txt" extension
- **Safari**: Use "ExportCookies" extension

**Note**: The cookies directory is automatically mounted in Docker. Place your cookies file in `./data/cookies/` on your host system.

## 🐛 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using ports
lsof -i :80
lsof -i :3000
lsof -i :8000

# Change ports in docker-compose.yml if needed
```

#### Storage Permissions
```bash
# Ensure Docker has access to data directory
chmod -R 755 ./data
chown -R $USER:$USER ./data
```

#### Memory Issues
- Whisper requires 4GB+ RAM for larger models
- Reduce model size if experiencing memory issues
- Monitor memory usage: `docker stats`

#### Video Processing Issues
```bash
# Check FFmpeg installation in container
docker-compose exec backend ffmpeg -version

# Verify video file formats
docker-compose exec backend file /app/videos/filename.mp4
```

#### Database Issues
```bash
# Reset database (WARNING: Deletes all data)
rm -rf data/db/*
docker-compose restart backend

# Backup database
cp data/db/tagTok.db data/db/tagTok_backup.db
```

### Performance Optimization

#### Backend Optimization
- Use smaller Whisper models for faster processing
- Implement video compression for large files
- Add database indexing for large datasets

#### Frontend Optimization
- Enable lazy loading for video thumbnails
- Implement virtual scrolling for large video lists
- Use React.memo for expensive components

### Monitoring and Logs

#### Application Logs
```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f nginx

# View logs with timestamps
docker-compose logs -t backend
```

#### Health Checks
```bash
# Check application health
curl http://localhost/api/health

# Check individual services
curl http://localhost:8000/health
curl http://localhost:3000
```

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest tests/
```

### Frontend Testing
```bash
cd frontend
npm test
```

### End-to-End Testing
```bash
# Run full application
docker-compose up -d

# Test upload functionality
curl -X POST -F "files=@test_video.mp4" http://localhost:8000/videos/upload

# Test API endpoints
curl http://localhost:8000/videos
curl http://localhost:8000/tags
curl http://localhost:8000/analytics
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

---

**tagTok** - Organize your TikTok videos with the power of AI! 🚀
