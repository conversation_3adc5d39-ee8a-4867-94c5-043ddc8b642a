#!/usr/bin/env python3
"""
Script to regenerate tags for the existing video using the improved tag generation system.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from models.database import Video, Tag, video_tags
from utils.ai_utils import TagGenerator
from services.tag_service import TagService

async def regenerate_video_tags():
    """Regenerate tags for the existing video with improved system"""
    print("🔄 Regenerating Video Tags with Improved System")
    print("=" * 60)
    
    # Create database connection
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///./tagtok.db')
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Get the video
        video = db.query(Video).first()
        if not video:
            print("❌ No video found in database")
            return
        
        print(f"📹 Video Found:")
        print(f"  ID: {video.id}")
        print(f"  Title: {video.title}")
        print(f"  Original Filename: {video.original_filename}")
        print(f"  Current Tags: {[tag.name for tag in video.tags]}")
        
        # Show current transcript
        if video.transcript:
            print(f"\n📝 Transcript: {video.transcript[:200]}...")
        else:
            print("\n❌ No transcript available")
            return
        
        # Generate new tags with improved system
        print(f"\n🚀 Generating new tags with improved system...")
        tag_generator = TagGenerator()
        
        # Use both transcript and title for analysis
        new_tags_data = await tag_generator.generate_tags(
            video.transcript, 
            title=video.title or video.original_filename,
            max_tags=8  # Allow more tags for cooking content
        )
        
        print(f"\n🏷️  New Generated Tags ({len(new_tags_data)}):")
        for i, tag_data in enumerate(new_tags_data, 1):
            print(f"  {i}. {tag_data['name']} - {tag_data['description']}")
        
        # Compare with old tags
        old_tag_names = [tag.name for tag in video.tags]
        new_tag_names = [tag_data['name'] for tag_data in new_tags_data]
        
        print(f"\n📊 Comparison:")
        print(f"  Old tags: {old_tag_names}")
        print(f"  New tags: {new_tag_names}")
        
        # Check improvements
        improvements = {
            "Removed 'Snaptik'": 'Snaptik' not in new_tag_names,
            "Fixed 'Pivienta' → 'Pimienta'": 'Pivienta' not in new_tag_names and any('pimienta' in tag.lower() for tag in new_tag_names),
            "Added cooking ingredients": any(ingredient in [tag.lower() for tag in new_tag_names] for ingredient in ['queso', 'maicena', 'morrón']),
            "Added cooking techniques": any(technique in [tag.lower() for tag in new_tag_names] for technique in ['rallar', 'hornear', 'mezclar']),
            "More relevant tags": len(new_tag_names) >= len(old_tag_names)
        }
        
        print(f"\n✅ Improvements:")
        for improvement, achieved in improvements.items():
            status = "✅" if achieved else "❌"
            print(f"  {status} {improvement}")
        
        # Ask if user wants to apply the changes
        print(f"\n🤔 Apply these improved tags to the video? (y/n): ", end="")
        response = input().strip().lower()
        
        if response == 'y' or response == 'yes':
            # Remove old tags
            print(f"\n🗑️  Removing old tags...")
            db.execute(video_tags.delete().where(video_tags.c.video_id == video.id))
            
            # Create new tags using TagService
            tag_service = TagService(db)
            
            print(f"🏷️  Creating new tags...")
            for tag_data in new_tags_data:
                try:
                    # Create or get existing tag
                    existing_tag = db.query(Tag).filter(Tag.name == tag_data['name']).first()
                    
                    if existing_tag:
                        tag = existing_tag
                        print(f"  ♻️  Using existing tag: {tag.name}")
                    else:
                        # Create new tag
                        tag = Tag(
                            name=tag_data['name'],
                            color=tag_data['color'],
                            description=tag_data['description'],
                            usage_count=0
                        )
                        db.add(tag)
                        db.flush()  # Get the ID
                        print(f"  ✨ Created new tag: {tag.name}")
                    
                    # Associate with video
                    db.execute(video_tags.insert().values(video_id=video.id, tag_id=tag.id))
                    
                    # Update usage count
                    tag.usage_count += 1
                    
                except Exception as e:
                    print(f"  ❌ Error creating tag {tag_data['name']}: {e}")
            
            # Commit changes
            db.commit()
            print(f"\n✅ Tags successfully updated!")
            
            # Show final result
            db.refresh(video)
            final_tags = [tag.name for tag in video.tags]
            print(f"🎉 Final tags: {final_tags}")
            
        else:
            print(f"\n❌ Changes not applied")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
    
    finally:
        db.close()

async def main():
    """Main function"""
    print("🧪 Video Tag Regeneration with Improved System")
    print("=" * 70)
    print("This will regenerate tags for the existing video using the improved")
    print("content-based analysis and Spanish grammar correction system.")
    print()
    
    await regenerate_video_tags()
    
    print(f"\n" + "=" * 70)
    print("✨ Tag regeneration completed!")
    print(f"\n🌐 Check the results at: http://localhost:3000/video/1")

if __name__ == "__main__":
    asyncio.run(main())
