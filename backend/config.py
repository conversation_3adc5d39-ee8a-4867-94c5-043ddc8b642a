"""
Centralized configuration management for tagTok backend
"""
import os
from typing import List


class Config:
    """Centralized configuration class for environment variables"""

    # Deployment Configuration
    PUBLIC_DOMAIN = os.getenv("PUBLIC_DOMAIN", "localhost")
    DEPLOYMENT_MODE = os.getenv("DEPLOYMENT_MODE", "development")  # development, staging, production
    APP_NAME = os.getenv("APP_NAME", "tagTok")
    APP_DESCRIPTION = os.getenv("APP_DESCRIPTION", "TikTok video organization platform")
    APP_VERSION = os.getenv("APP_VERSION", "1.0.0")

    # Feature Flags
    ENABLE_REGISTRATION = os.getenv("ENABLE_REGISTRATION", "true").lower() == "true"
    ENABLE_PUBLIC_SHARING = os.getenv("ENABLE_PUBLIC_SHARING", "true").lower() == "true"

    # Database Configuration
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")

    # Directory Paths
    VIDEOS_DIR = os.getenv("VIDEOS_DIR", "/app/videos")
    TRANSCRIPTS_DIR = os.getenv("TRANSCRIPTS_DIR", "/app/transcripts")

    # Port Configuration
    BACKEND_INTERNAL_PORT = int(os.getenv("BACKEND_INTERNAL_PORT", "8000"))
    BACKEND_EXTERNAL_PORT = int(os.getenv("BACKEND_EXTERNAL_PORT", "8080"))
    FRONTEND_EXTERNAL_PORT = int(os.getenv("FRONTEND_EXTERNAL_PORT", "3001"))
    NGINX_PORT = int(os.getenv("NGINX_PORT", "8790"))
    OLLAMA_INTERNAL_PORT = int(os.getenv("OLLAMA_INTERNAL_PORT", "11434"))

    # Service URLs
    BACKEND_URL = os.getenv("BACKEND_URL", "http://backend:8000")
    OLLAMA_URL = os.getenv("OLLAMA_URL", "http://ollama:11434")
    FRONTEND_URL = os.getenv("FRONTEND_URL", "http://frontend:80")

    # AI Model Configuration
    WHISPER_MODEL_SIZE = os.getenv("WHISPER_MODEL_SIZE", "base")

    # Authentication Configuration
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    ALGORITHM = "HS256"

    # yt-dlp Configuration
    YTDLP_COOKIES_FILE = os.getenv("YTDLP_COOKIES_FILE", None)
    YTDLP_COOKIES_FROM_BROWSER = os.getenv("YTDLP_COOKIES_FROM_BROWSER", None)

    # Development Configuration
    PYTHONPATH = os.getenv("PYTHONPATH", "/app")
    
    @classmethod
    def get_ollama_endpoints(cls) -> List[str]:
        """Get Ollama endpoints with fallbacks for different environments"""
        base_url = cls.OLLAMA_URL
        
        # Extract host and port from OLLAMA_URL
        if "://" in base_url:
            protocol, rest = base_url.split("://", 1)
            if ":" in rest:
                host, port = rest.split(":", 1)
            else:
                host = rest
                port = str(cls.OLLAMA_INTERNAL_PORT)
        else:
            host = "ollama"
            port = str(cls.OLLAMA_INTERNAL_PORT)
        
        # Return endpoints with fallbacks
        endpoints = [
            f"http://{host}:{port}/api/generate",  # Primary endpoint
        ]
        
        # Add localhost fallback for development
        if host != "localhost":
            endpoints.append(f"http://localhost:{port}/api/generate")
        
        return endpoints
    
    @classmethod
    def get_backend_health_url(cls) -> str:
        """Get backend health check URL"""
        return f"http://localhost:{cls.BACKEND_INTERNAL_PORT}/health"
    
    @classmethod
    def get_cors_origins(cls) -> List[str]:
        """Get CORS origins based on deployment configuration"""
        cors_origins_env = os.getenv("CORS_ORIGINS", "")

        if cors_origins_env:
            # Use explicitly set CORS origins
            return [origin.strip() for origin in cors_origins_env.split(",") if origin.strip()]

        # Auto-generate CORS origins based on deployment mode and domain
        origins = []

        if cls.DEPLOYMENT_MODE == "production":
            # Production: only allow the public domain
            if cls.PUBLIC_DOMAIN != "localhost":
                origins.extend([
                    f"https://{cls.PUBLIC_DOMAIN}",
                    f"http://{cls.PUBLIC_DOMAIN}",  # Fallback for non-HTTPS
                ])
        else:
            # Development/Staging: allow localhost and the public domain
            origins.extend([
                f"http://localhost:{cls.FRONTEND_EXTERNAL_PORT}",
                f"http://localhost:{cls.NGINX_PORT}",
                "http://127.0.0.1:3000",  # Common React dev port
                "http://127.0.0.1:3001",
            ])

            if cls.PUBLIC_DOMAIN != "localhost":
                origins.extend([
                    f"https://{cls.PUBLIC_DOMAIN}",
                    f"http://{cls.PUBLIC_DOMAIN}",
                ])

        return origins

    @classmethod
    def get_api_url_for_frontend(cls) -> str:
        """Get the API URL that the frontend should use"""
        api_url_env = os.getenv("REACT_APP_API_URL", "")

        if api_url_env:
            return api_url_env

        # Auto-generate API URL based on deployment mode
        if cls.DEPLOYMENT_MODE == "production" and cls.PUBLIC_DOMAIN != "localhost":
            return f"https://{cls.PUBLIC_DOMAIN}/api"
        else:
            # Development: use nginx proxy
            return f"http://localhost:{cls.NGINX_PORT}/api"

    @classmethod
    def is_production(cls) -> bool:
        """Check if running in production mode"""
        return cls.DEPLOYMENT_MODE == "production"

    @classmethod
    def is_development(cls) -> bool:
        """Check if running in development mode"""
        return cls.DEPLOYMENT_MODE == "development"

    @classmethod
    def print_config(cls):
        """Print current configuration (for debugging)"""
        print(f"=== {cls.APP_NAME} Configuration ===")
        print(f"App Name: {cls.APP_NAME}")
        print(f"App Version: {cls.APP_VERSION}")
        print(f"Public Domain: {cls.PUBLIC_DOMAIN}")
        print(f"Deployment Mode: {cls.DEPLOYMENT_MODE}")
        print(f"Enable Registration: {cls.ENABLE_REGISTRATION}")
        print(f"Enable Public Sharing: {cls.ENABLE_PUBLIC_SHARING}")
        print(f"Database URL: {cls.DATABASE_URL}")
        print(f"Videos Directory: {cls.VIDEOS_DIR}")
        print(f"Transcripts Directory: {cls.TRANSCRIPTS_DIR}")
        print(f"Backend Internal Port: {cls.BACKEND_INTERNAL_PORT}")
        print(f"Backend External Port: {cls.BACKEND_EXTERNAL_PORT}")
        print(f"Nginx Port: {cls.NGINX_PORT}")
        print(f"Backend URL: {cls.BACKEND_URL}")
        print(f"Ollama URL: {cls.OLLAMA_URL}")
        print(f"Ollama Endpoints: {cls.get_ollama_endpoints()}")
        print(f"Frontend URL: {cls.FRONTEND_URL}")
        print(f"CORS Origins: {cls.get_cors_origins()}")
        print(f"API URL for Frontend: {cls.get_api_url_for_frontend()}")
        print(f"Whisper Model Size: {cls.WHISPER_MODEL_SIZE}")
        print("=" * 50)


# Create a global config instance
config = Config()
