#!/usr/bin/env python3
"""
Migration script to add transcript refinement fields to the videos table.
This migration adds:
- raw_transcript: Original Whisper transcript
- transcript_refined: Boolean flag indicating if transcript was LLM-refined
- transcript_refinement_confidence: Confidence score for the refinement
"""

import os
import sys
import sqlite3
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

def add_transcript_refinement_columns():
    """Add transcript refinement columns to the videos table"""
    # Get database path from environment or use default
    db_path = os.getenv("DATABASE_URL", "sqlite:///data/db/tagTok.db")
    if db_path.startswith("sqlite:///"):
        db_file_path = db_path.replace("sqlite:///", "")
    else:
        print(f"Unsupported database URL: {db_path}")
        return False
    
    if not os.path.exists(db_file_path):
        print(f"Database file {db_file_path} does not exist")
        return False
    
    try:
        conn = sqlite3.connect(db_file_path)
        cursor = conn.cursor()
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(videos)")
        columns = [column[1] for column in cursor.fetchall()]
        
        columns_to_add = []
        if 'raw_transcript' not in columns:
            columns_to_add.append(('raw_transcript', 'TEXT'))
        if 'transcript_refined' not in columns:
            columns_to_add.append(('transcript_refined', 'BOOLEAN DEFAULT 0'))
        if 'transcript_refinement_confidence' not in columns:
            columns_to_add.append(('transcript_refinement_confidence', 'REAL'))
        
        if not columns_to_add:
            print("✅ All transcript refinement columns already exist")
            conn.close()
            return True
        
        print(f"Adding {len(columns_to_add)} new columns to videos table...")
        
        # Add new columns
        for column_name, column_type in columns_to_add:
            sql = f"ALTER TABLE videos ADD COLUMN {column_name} {column_type}"
            print(f"  Adding column: {column_name}")
            cursor.execute(sql)
        
        # For existing videos, copy transcript to raw_transcript if it exists
        print("Migrating existing transcript data...")
        cursor.execute("""
            UPDATE videos 
            SET raw_transcript = transcript 
            WHERE transcript IS NOT NULL 
            AND raw_transcript IS NULL
        """)
        
        migrated_count = cursor.rowcount
        if migrated_count > 0:
            print(f"  Migrated {migrated_count} existing transcripts to raw_transcript field")
        
        conn.commit()
        conn.close()
        
        print("✅ Transcript refinement migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def verify_migration():
    """Verify that the migration was successful"""
    db_path = os.getenv("DATABASE_URL", "sqlite:///data/db/tagTok.db")
    if db_path.startswith("sqlite:///"):
        db_file_path = db_path.replace("sqlite:///", "")
    else:
        return False
    
    try:
        conn = sqlite3.connect(db_file_path)
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(videos)")
        columns = {column[1]: column[2] for column in cursor.fetchall()}
        
        required_columns = {
            'raw_transcript': 'TEXT',
            'transcript_refined': 'BOOLEAN',
            'transcript_refinement_confidence': 'REAL'
        }
        
        print("\n🔍 Verifying migration...")
        all_good = True
        
        for col_name, expected_type in required_columns.items():
            if col_name in columns:
                print(f"  ✅ {col_name}: {columns[col_name]}")
            else:
                print(f"  ❌ {col_name}: MISSING")
                all_good = False
        
        # Check data migration
        cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(transcript) as with_transcript,
                   COUNT(raw_transcript) as with_raw_transcript
            FROM videos
        """)
        
        total, with_transcript, with_raw_transcript = cursor.fetchone()
        print(f"\n📊 Data verification:")
        print(f"  Total videos: {total}")
        print(f"  Videos with transcript: {with_transcript}")
        print(f"  Videos with raw_transcript: {with_raw_transcript}")
        
        if with_transcript > 0 and with_raw_transcript == 0:
            print("  ⚠️  Warning: Videos have transcripts but no raw_transcripts")
            all_good = False
        
        conn.close()
        
        if all_good:
            print("\n✅ Migration verification successful!")
        else:
            print("\n❌ Migration verification failed!")
        
        return all_good
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main migration function"""
    print("🔄 Starting transcript refinement migration...")
    print("=" * 60)
    
    # Run migration
    if add_transcript_refinement_columns():
        # Verify migration
        verify_migration()
        
        print("\n📝 Migration Summary:")
        print("- Added raw_transcript column to store original Whisper output")
        print("- Added transcript_refined boolean flag")
        print("- Added transcript_refinement_confidence score")
        print("- Migrated existing transcript data to raw_transcript field")
        print("\n🎉 Transcript refinement feature is now ready!")
        print("\nNext steps:")
        print("1. Restart the application to use the new fields")
        print("2. New videos will automatically use transcript refinement")
        print("3. Existing videos can be reprocessed to get refined transcripts")
        
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
