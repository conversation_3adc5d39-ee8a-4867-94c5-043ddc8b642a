from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from models.database import get_db, User
from models.schemas import TagResponse, TagCreate, TagUpdate
from services.tag_service import TagService
from middleware.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=List[TagResponse])
async def get_tags(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    search: Optional[str] = Query(None, description="Search tag names"),
    sort_by: str = Query("usage_count", description="Sort by: name, usage_count, created_date"),
    order: str = Query("desc", description="Order: asc, desc"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of tags with optional filtering and sorting - filtered by user permissions"""
    tag_service = TagService(db)
    return tag_service.get_tags(
        skip=skip,
        limit=limit,
        search=search,
        sort_by=sort_by,
        order=order,
        user_id=current_user.id
    )

@router.get("/{tag_id}", response_model=TagResponse)
async def get_tag(tag_id: int, db: Session = Depends(get_db)):
    """Get a specific tag by ID"""
    tag_service = TagService(db)
    tag = tag_service.get_tag_by_id(tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    return tag

@router.post("/", response_model=TagResponse)
async def create_tag(tag_data: TagCreate, db: Session = Depends(get_db)):
    """Create a new tag"""
    tag_service = TagService(db)
    
    # Check if tag with same name already exists
    existing_tag = tag_service.get_tag_by_name(tag_data.name)
    if existing_tag:
        raise HTTPException(
            status_code=400, 
            detail="Tag with this name already exists"
        )
    
    return tag_service.create_tag(tag_data)

@router.put("/{tag_id}", response_model=TagResponse)
async def update_tag(
    tag_id: int, 
    tag_update: TagUpdate, 
    db: Session = Depends(get_db)
):
    """Update an existing tag"""
    tag_service = TagService(db)
    
    # Check if tag exists
    existing_tag = tag_service.get_tag_by_id(tag_id)
    if not existing_tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    
    # Check if new name conflicts with existing tag
    if tag_update.name and tag_update.name != existing_tag.name:
        name_conflict = tag_service.get_tag_by_name(tag_update.name)
        if name_conflict:
            raise HTTPException(
                status_code=400,
                detail="Tag with this name already exists"
            )
    
    return tag_service.update_tag(tag_id, tag_update)

@router.delete("/{tag_id}")
async def delete_tag(tag_id: int, db: Session = Depends(get_db)):
    """Delete a tag"""
    tag_service = TagService(db)
    
    tag = tag_service.get_tag_by_id(tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    
    # Check if tag is used by any videos
    if tag.usage_count > 0:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete tag that is used by videos. Remove tag from videos first."
        )
    
    success = tag_service.delete_tag(tag_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete tag")
    
    return {"message": "Tag deleted successfully"}

@router.post("/{tag_id}/videos/{video_id}")
async def add_tag_to_video(
    tag_id: int, 
    video_id: int, 
    db: Session = Depends(get_db)
):
    """Add a tag to a video"""
    tag_service = TagService(db)
    success = tag_service.add_tag_to_video(tag_id, video_id)
    if not success:
        raise HTTPException(
            status_code=400, 
            detail="Failed to add tag to video. Check if tag and video exist."
        )
    return {"message": "Tag added to video successfully"}

@router.delete("/{tag_id}/videos/{video_id}")
async def remove_tag_from_video(
    tag_id: int, 
    video_id: int, 
    db: Session = Depends(get_db)
):
    """Remove a tag from a video"""
    tag_service = TagService(db)
    success = tag_service.remove_tag_from_video(tag_id, video_id)
    if not success:
        raise HTTPException(
            status_code=400,
            detail="Failed to remove tag from video"
        )
    return {"message": "Tag removed from video successfully"}

@router.get("/cloud/data")
async def get_tag_cloud_data(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get data for tag cloud visualization - filtered by user permissions"""
    tag_service = TagService(db)
    return tag_service.get_tag_cloud_data(user_id=current_user.id)
