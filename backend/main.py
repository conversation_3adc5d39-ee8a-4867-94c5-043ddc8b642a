from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, UploadFile, File, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uvicorn
from datetime import datetime

# Import models and dependencies
from models.database import create_tables, get_db
from models.schemas import (
    VideoResponse, TagResponse, AnalyticsResponse, UploadResponse,
    HealthResponse, VideoFilter, PaginationParams, TagCreate, TagUpdate,
    VideoUpdate, ExportFormat, ErrorResponse, DownloadRequest, DownloadResponse
)

# Import routers
from routers import videos, tags, analytics, export, recipes, auth, sharing

# Import configuration
from config import config

# Create FastAPI app
app = FastAPI(
    title=f"{config.APP_NAME} API",
    description=config.APP_DESCRIPTION,
    version=config.APP_VERSION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware - use configuration-based origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.get_cors_origins(),  # Automatically configured based on deployment mode
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    create_tables()

    # Create necessary directories using config
    os.makedirs(config.VIDEOS_DIR.replace("/app/", ""), exist_ok=True)
    os.makedirs("db", exist_ok=True)
    os.makedirs(config.TRANSCRIPTS_DIR.replace("/app/", ""), exist_ok=True)

    # Print configuration for debugging
    if config.is_development():
        config.print_config()

# Include routers
app.include_router(auth.router, prefix="/auth", tags=["authentication"])
app.include_router(sharing.router, prefix="/sharing", tags=["sharing"])
app.include_router(videos.router, prefix="/videos", tags=["videos"])
app.include_router(tags.router, prefix="/tags", tags=["tags"])
app.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
app.include_router(export.router, prefix="/export", tags=["export"])
app.include_router(recipes.router, prefix="/recipes", tags=["recipes"])

# Health check endpoint - lightweight, no DB dependency
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Lightweight health check that doesn't hit the database"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now()
    )

# Simple status endpoint for server load monitoring
@app.get("/status")
async def server_status():
    """Very lightweight status check"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": f"{config.APP_NAME} API",
        "version": config.APP_VERSION,
        "docs": "/docs",
        "deployment_mode": config.DEPLOYMENT_MODE,
        "registration_enabled": config.ENABLE_REGISTRATION
    }

# Note: Video and thumbnail serving is now handled by the videos router with proper authentication
# These endpoints are deprecated and should not be used

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            detail="Internal server error",
            error_code="INTERNAL_ERROR"
        ).dict()
    )

if __name__ == "__main__":
    # Use configuration for port
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=config.BACKEND_INTERNAL_PORT,
        reload=config.is_development()
    )
