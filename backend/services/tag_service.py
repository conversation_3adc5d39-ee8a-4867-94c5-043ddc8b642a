from sqlalchemy.orm import Session
from sqlalchemy import func, desc, asc
from typing import List, Optional, Dict, Any

from models.database import Tag, Video, video_tags
from models.schemas import TagCreate, TagUpdate
from services.sharing_service import SharingService

class TagService:
    def __init__(self, db: Session):
        self.db = db

    def _get_accessible_video_ids(self, user_id: int) -> List[int]:
        """Get list of video IDs that the user has access to (owns or has been shared with)"""
        # Get videos owned by the user
        owned_videos = self.db.query(Video.id).filter(Video.user_id == user_id).all()
        owned_video_ids = [video.id for video in owned_videos]

        # Get videos shared with the user
        shared_video_ids = []
        all_videos = self.db.query(Video).all()
        for video in all_videos:
            if video.id not in owned_video_ids:
                permission = SharingService.get_user_permission(self.db, video.id, user_id)
                if permission:
                    shared_video_ids.append(video.id)

        return owned_video_ids + shared_video_ids
    
    def get_tags(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        sort_by: str = "usage_count",
        order: str = "desc",
        user_id: Optional[int] = None
    ) -> List[Tag]:
        """Get tags with optional filtering and sorting, filtered by user permissions"""
        if user_id is None:
            # If no user specified, return empty list for security
            return []

        # Get all videos the user has access to
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        # Query tags that are associated with accessible videos
        query = self.db.query(Tag).join(video_tags).filter(
            video_tags.c.video_id.in_(accessible_video_ids)
        ).distinct()

        # Apply search filter
        if search:
            search_term = f"%{search}%"
            query = query.filter(Tag.name.ilike(search_term))

        # Apply sorting
        if sort_by == "name":
            order_func = asc if order == "asc" else desc
            query = query.order_by(order_func(Tag.name))
        elif sort_by == "created_date":
            order_func = asc if order == "asc" else desc
            query = query.order_by(order_func(Tag.created_date))
        else:  # usage_count
            order_func = asc if order == "asc" else desc
            query = query.order_by(order_func(Tag.usage_count))

        return query.offset(skip).limit(limit).all()
    
    def get_tag_by_id(self, tag_id: int) -> Optional[Tag]:
        """Get a tag by its ID"""
        return self.db.query(Tag).filter(Tag.id == tag_id).first()
    
    def get_tag_by_name(self, name: str) -> Optional[Tag]:
        """Get a tag by its name"""
        return self.db.query(Tag).filter(Tag.name == name).first()
    
    def create_tag(self, tag_data: TagCreate) -> Tag:
        """Create a new tag"""
        tag = Tag(
            name=tag_data.name,
            color=tag_data.color,
            description=tag_data.description,
            usage_count=0
        )
        
        self.db.add(tag)
        self.db.commit()
        self.db.refresh(tag)
        return tag
    
    def update_tag(self, tag_id: int, tag_update: TagUpdate) -> Optional[Tag]:
        """Update an existing tag"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return None
        
        # Update fields
        if tag_update.name is not None:
            tag.name = tag_update.name
        if tag_update.color is not None:
            tag.color = tag_update.color
        if tag_update.description is not None:
            tag.description = tag_update.description
        
        self.db.commit()
        self.db.refresh(tag)
        return tag
    
    def delete_tag(self, tag_id: int) -> bool:
        """Delete a tag"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False
        
        try:
            self.db.delete(tag)
            self.db.commit()
            return True
        except Exception as e:
            print(f"Error deleting tag {tag_id}: {e}")
            self.db.rollback()
            return False
    
    def add_tag_to_video(self, tag_id: int, video_id: int) -> bool:
        """Add a tag to a video"""
        tag = self.get_tag_by_id(tag_id)
        video = self.db.query(Video).filter(Video.id == video_id).first()
        
        if not tag or not video:
            return False
        
        # Check if tag is already associated with video
        if tag in video.tags:
            return True  # Already associated
        
        try:
            video.tags.append(tag)
            tag.usage_count += 1
            self.db.commit()
            return True
        except Exception as e:
            print(f"Error adding tag {tag_id} to video {video_id}: {e}")
            self.db.rollback()
            return False
    
    def remove_tag_from_video(self, tag_id: int, video_id: int) -> bool:
        """Remove a tag from a video"""
        tag = self.get_tag_by_id(tag_id)
        video = self.db.query(Video).filter(Video.id == video_id).first()
        
        if not tag or not video:
            return False
        
        # Check if tag is associated with video
        if tag not in video.tags:
            return True  # Not associated
        
        try:
            video.tags.remove(tag)
            tag.usage_count = max(0, tag.usage_count - 1)
            self.db.commit()
            return True
        except Exception as e:
            print(f"Error removing tag {tag_id} from video {video_id}: {e}")
            self.db.rollback()
            return False
    
    def get_tag_cloud_data(self, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get data for tag cloud visualization, filtered by user permissions"""
        if user_id is None:
            # If no user specified, return empty list for security
            return []

        # Get all videos the user has access to
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        # Query tags that are associated with accessible videos and calculate usage count
        # for only those accessible videos
        tag_usage = self.db.query(
            Tag.id,
            Tag.name,
            Tag.color,
            Tag.description,
            func.count(video_tags.c.video_id).label('accessible_usage_count')
        ).join(video_tags).filter(
            video_tags.c.video_id.in_(accessible_video_ids)
        ).group_by(Tag.id, Tag.name, Tag.color, Tag.description).all()

        tag_cloud_data = []
        for tag_id, name, color, description, usage_count in tag_usage:
            if usage_count > 0:
                tag_cloud_data.append({
                    "id": tag_id,
                    "name": name,
                    "color": color,
                    "description": description,
                    "usage_count": usage_count,
                    "size": min(max(usage_count * 10, 12), 48)  # Scale size between 12-48px
                })

        # Sort by usage count
        tag_cloud_data.sort(key=lambda x: x["usage_count"], reverse=True)
        return tag_cloud_data
    
    def get_tags_by_video(self, video_id: int) -> List[Tag]:
        """Get all tags associated with a video"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            return []
        return video.tags
    
    def update_tag_usage_counts(self):
        """Recalculate usage counts for all tags"""
        tags = self.db.query(Tag).all()
        
        for tag in tags:
            # Count videos associated with this tag
            count = self.db.query(video_tags).filter(
                video_tags.c.tag_id == tag.id
            ).count()
            tag.usage_count = count
        
        self.db.commit()
    
    def get_popular_tags(self, limit: int = 10, user_id: Optional[int] = None) -> List[Tag]:
        """Get most popular tags by usage count, filtered by user permissions"""
        if user_id is None:
            # If no user specified, return empty list for security
            return []

        # Get all videos the user has access to
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        # Query tags that are associated with accessible videos and calculate usage count
        # for only those accessible videos
        tag_usage = self.db.query(
            Tag,
            func.count(video_tags.c.video_id).label('accessible_usage_count')
        ).join(video_tags).filter(
            video_tags.c.video_id.in_(accessible_video_ids)
        ).group_by(Tag.id).having(
            func.count(video_tags.c.video_id) > 0
        ).order_by(desc(func.count(video_tags.c.video_id))).limit(limit).all()

        # Extract just the Tag objects
        return [tag for tag, usage_count in tag_usage]
    
    def search_tags(self, query: str, limit: int = 20) -> List[Tag]:
        """Search tags by name"""
        search_term = f"%{query}%"
        return self.db.query(Tag).filter(
            Tag.name.ilike(search_term)
        ).order_by(desc(Tag.usage_count)).limit(limit).all()

    def bulk_add_tags_to_videos(self, tag_ids: List[int], video_ids: List[int]) -> Dict[str, Any]:
        """Add multiple tags to multiple videos"""
        result = {
            "success": True,
            "processed_count": 0,
            "failed_count": 0,
            "failed_items": [],
            "updated_videos": []
        }

        try:
            # Validate tags exist
            tags = self.db.query(Tag).filter(Tag.id.in_(tag_ids)).all()
            if len(tags) != len(tag_ids):
                found_tag_ids = [tag.id for tag in tags]
                missing_tag_ids = [tid for tid in tag_ids if tid not in found_tag_ids]
                result["success"] = False
                result["failed_items"].append({
                    "error": f"Tags not found: {missing_tag_ids}",
                    "type": "validation"
                })
                return result

            # Validate videos exist
            videos = self.db.query(Video).filter(Video.id.in_(video_ids)).all()
            if len(videos) != len(video_ids):
                found_video_ids = [video.id for video in videos]
                missing_video_ids = [vid for vid in video_ids if vid not in found_video_ids]
                result["success"] = False
                result["failed_items"].append({
                    "error": f"Videos not found: {missing_video_ids}",
                    "type": "validation"
                })
                return result

            # Process each video
            for video in videos:
                try:
                    tags_added = 0
                    for tag in tags:
                        if tag not in video.tags:
                            video.tags.append(tag)
                            tag.usage_count += 1
                            tags_added += 1

                    if tags_added > 0:
                        result["updated_videos"].append(video.id)
                    result["processed_count"] += 1

                except Exception as e:
                    result["failed_count"] += 1
                    result["failed_items"].append({
                        "video_id": video.id,
                        "error": str(e),
                        "type": "processing"
                    })

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            result["success"] = False
            result["failed_items"].append({
                "error": f"Database error: {str(e)}",
                "type": "database"
            })

        return result

    def bulk_remove_tags_from_videos(self, tag_ids: List[int], video_ids: List[int]) -> Dict[str, Any]:
        """Remove multiple tags from multiple videos"""
        result = {
            "success": True,
            "processed_count": 0,
            "failed_count": 0,
            "failed_items": [],
            "updated_videos": []
        }

        try:
            # Validate tags exist
            tags = self.db.query(Tag).filter(Tag.id.in_(tag_ids)).all()
            if len(tags) != len(tag_ids):
                found_tag_ids = [tag.id for tag in tags]
                missing_tag_ids = [tid for tid in tag_ids if tid not in found_tag_ids]
                result["success"] = False
                result["failed_items"].append({
                    "error": f"Tags not found: {missing_tag_ids}",
                    "type": "validation"
                })
                return result

            # Validate videos exist
            videos = self.db.query(Video).filter(Video.id.in_(video_ids)).all()
            if len(videos) != len(video_ids):
                found_video_ids = [video.id for video in videos]
                missing_video_ids = [vid for vid in video_ids if vid not in found_video_ids]
                result["success"] = False
                result["failed_items"].append({
                    "error": f"Videos not found: {missing_video_ids}",
                    "type": "validation"
                })
                return result

            # Process each video
            for video in videos:
                try:
                    tags_removed = 0
                    for tag in tags:
                        if tag in video.tags:
                            video.tags.remove(tag)
                            tag.usage_count = max(0, tag.usage_count - 1)
                            tags_removed += 1

                    if tags_removed > 0:
                        result["updated_videos"].append(video.id)
                    result["processed_count"] += 1

                except Exception as e:
                    result["failed_count"] += 1
                    result["failed_items"].append({
                        "video_id": video.id,
                        "error": str(e),
                        "type": "processing"
                    })

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            result["success"] = False
            result["failed_items"].append({
                "error": f"Database error: {str(e)}",
                "type": "database"
            })

        return result
