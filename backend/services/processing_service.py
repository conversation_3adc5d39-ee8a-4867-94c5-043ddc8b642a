import asyncio
import os
import subprocess
import tempfile
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import random

from models.database import Video, Tag, ProcessingJob
from datetime import datetime
from services.video_service import VideoService
from services.tag_service import TagService
from services.recipe_service import RecipeService
from services.transcript_refinement_service import TranscriptRefinementService
from utils.ai_utils import WhisperTranscriber, TagGenerator
from utils.recipe_extractor import RecipeExtractor
from utils.video_utils import extract_video_frames

class ProcessingService:
    # Class-level semaphore to limit concurrent processing across all instances
    _processing_semaphore = asyncio.Semaphore(2)  # Max 2 concurrent video processing operations

    def __init__(self, db: Session):
        self.db = db
        self.video_service = VideoService(db)
        self.tag_service = TagService(db)
        self.recipe_service = RecipeService(db)
        self.transcript_refinement_service = TranscriptRefinementService()
        self.transcriber = WhisperTranscriber()
        self.tag_generator = TagGenerator()
        self.recipe_extractor = RecipeExtractor()
    
    async def process_video_async(self, video_id: int):
        """Process a video asynchronously (transcription + tagging)"""
        # Use semaphore to limit concurrent processing operations
        async with self._processing_semaphore:
            try:
                # Update status to processing with 0% progress
                self.video_service.update_processing_status(video_id, "processing", progress=0)

                # Create processing job
                job = ProcessingJob(
                    video_id=video_id,
                    job_type="full_processing",
                    status="running",
                    started_at=datetime.now()
                )
                self.db.add(job)
                self.db.commit()

                # Get video
                video = self.video_service.get_video_by_id(video_id)
                if not video:
                    raise Exception(f"Video {video_id} not found")

                # Step 1: Transcribe video (0% -> 50%)
                self.video_service.update_processing_status(video_id, "processing", progress=10)
                print(f"Starting transcription for video {video_id}")

                raw_transcript, language = await self.transcribe_video(video.file_path)

                self.video_service.update_processing_status(video_id, "processing", progress=50)
                print(f"Transcription completed for video {video_id}")

                # Step 2: Refine transcript using LLM (50% -> 70%)
                self.video_service.update_processing_status(video_id, "processing", progress=55)
                print(f"Starting transcript refinement for video {video_id}")

                refined_transcript, refinement_confidence = await self.refine_transcript(
                    raw_transcript, language
                )

                self.video_service.update_processing_status(video_id, "processing", progress=70)
                print(f"Transcript refinement completed for video {video_id} with confidence {refinement_confidence:.2f}")

                # Step 3: Generate tags from refined transcript (70% -> 85%)
                self.video_service.update_processing_status(video_id, "processing", progress=75)
                print(f"Starting tag generation for video {video_id}")

                suggested_tags = await self.generate_tags_from_transcript(
                    refined_transcript, video.title or video.original_filename
                )

                self.video_service.update_processing_status(video_id, "processing", progress=85)
                print(f"Tag generation completed for video {video_id}")

                # Step 4: Extract recipe if it's a cooking video (85% -> 95%)
                self.video_service.update_processing_status(video_id, "processing", progress=87)
                print(f"Starting recipe extraction for video {video_id}")

                recipe_create = await self.recipe_extractor.extract_recipe(
                    refined_transcript, video.title or video.original_filename, video_id
                )

                if recipe_create:
                    try:
                        recipe = self.recipe_service.create_recipe(recipe_create)
                        print(f"Recipe extracted and saved for video {video_id} with confidence {recipe_create.extraction_confidence}")
                    except Exception as e:
                        print(f"Failed to save recipe for video {video_id}: {e}")
                else:
                    print(f"No recipe detected for video {video_id}")

                # Step 5: Save tags and finalize (95% -> 100%)
                self.video_service.update_processing_status(video_id, "processing", progress=95)

                # Create and assign tags with deduplication
                self._assign_tags_to_video(video_id, suggested_tags)

                # Update video with refined transcript and refinement metadata (100% complete)
                self.video_service.update_processing_status_with_refinement(
                    video_id, "completed", refined_transcript, language,
                    raw_transcript, refinement_confidence, progress=100
                )

                # Update job status
                job.status = "completed"
                job.completed_at = datetime.now()
                self.db.commit()

                print(f"Video {video_id} processing completed successfully")

            except Exception as e:
                print(f"Error processing video {video_id}: {e}")

                # Update status to failed
                self.video_service.update_processing_status(video_id, "failed")

                # Update job status
                if 'job' in locals():
                    job.status = "failed"
                    job.error_message = str(e)
                    self.db.commit()
    
    async def transcribe_video(self, video_path: str) -> tuple[str, str]:
        """Transcribe video using Whisper"""
        try:
            transcript, language = await self.transcriber.transcribe(video_path)
            return transcript, language
        except Exception as e:
            print(f"Transcription failed for {video_path}: {e}")
            return "", "unknown"

    async def refine_transcript(self, raw_transcript: str, language: str) -> tuple[str, float]:
        """Refine transcript using LLM with fallback to original"""
        try:
            refined_transcript, confidence = await self.transcript_refinement_service.refine_transcript(
                raw_transcript, language
            )

            # If refinement failed or confidence is too low, use original
            if confidence < 0.3:
                print(f"Refinement confidence too low ({confidence:.2f}), using original transcript")
                return raw_transcript, 0.0

            return refined_transcript, confidence

        except Exception as e:
            print(f"Transcript refinement failed: {e}")
            return raw_transcript, 0.0

    async def generate_tags_from_transcript(
        self,
        transcript: str,
        title: str = ""
    ) -> List[Dict[str, str]]:
        """Generate content-based tags from transcript and title with improved analysis"""
        try:
            if not transcript.strip():
                return []

            # Generate tags using improved content analysis
            # Pass title separately for better filtering of technical metadata
            tags = await self.tag_generator.generate_tags(transcript, title=title)
            return tags
        except Exception as e:
            print(f"Tag generation failed: {e}")
            return []
    
    def reprocess_video(self, video_id: int):
        """Queue video for reprocessing (synchronous version)"""
        # Reset processing status
        self.video_service.update_processing_status(video_id, "pending")

        # Clear existing tags before reprocessing
        self._clear_video_tags(video_id)

        # Queue for processing
        asyncio.create_task(self.process_video_async(video_id))

    async def reprocess_video_async(self, video_id: int):
        """Reprocess a video asynchronously with tag replacement"""
        # Reset processing status
        self.video_service.update_processing_status(video_id, "pending")

        # Clear existing tags before reprocessing
        self._clear_video_tags(video_id)

        # Process the video (this will generate new tags)
        await self.process_video_async(video_id)

    def _clear_video_tags(self, video_id: int):
        """Clear all existing tags from a video"""
        video = self.video_service.get_video_by_id(video_id)
        if video and video.tags:
            print(f"Clearing {len(video.tags)} existing tags from video {video_id} before reprocessing")
            # Remove all tags from video
            for tag in video.tags[:]:  # Create a copy to avoid modification during iteration
                self.tag_service.remove_tag_from_video(tag.id, video_id)
            print(f"Successfully cleared tags from video {video_id}")

    def _assign_tags_to_video(self, video_id: int, suggested_tags: List[Dict[str, str]], max_tags: int = 5):
        """Assign tags to video with deduplication and limit enforcement"""
        if not suggested_tags:
            print(f"No tags to assign to video {video_id}")
            return

        # Get current video tags to check for duplicates
        video = self.video_service.get_video_by_id(video_id)
        if not video:
            print(f"Video {video_id} not found")
            return

        current_tag_names = {tag.name.lower() for tag in video.tags}
        assigned_count = 0

        print(f"Assigning up to {max_tags} tags to video {video_id}")
        print(f"Current tags: {list(current_tag_names)}")
        print(f"Suggested tags: {[tag['name'] for tag in suggested_tags]}")

        for tag_data in suggested_tags:
            if assigned_count >= max_tags:
                print(f"Reached maximum of {max_tags} tags for video {video_id}")
                break

            tag_name = tag_data["name"]

            # Skip if tag already exists on this video (case-insensitive)
            if tag_name.lower() in current_tag_names:
                print(f"Skipping duplicate tag '{tag_name}' for video {video_id}")
                continue

            # Check if tag exists in database
            existing_tag = self.tag_service.get_tag_by_name(tag_name)
            if not existing_tag:
                # Create new tag
                from models.schemas import TagCreate
                tag_create = TagCreate(
                    name=tag_data["name"],
                    color=tag_data["color"],
                    description=tag_data["description"]
                )
                tag = self.tag_service.create_tag(tag_create)
                print(f"Created new tag '{tag_name}' for video {video_id}")
            else:
                tag = existing_tag
                print(f"Using existing tag '{tag_name}' for video {video_id}")

            # Add tag to video
            success = self.tag_service.add_tag_to_video(tag.id, video_id)
            if success:
                current_tag_names.add(tag_name.lower())
                assigned_count += 1
                print(f"Successfully assigned tag '{tag_name}' to video {video_id}")
            else:
                print(f"Failed to assign tag '{tag_name}' to video {video_id}")

        print(f"Assigned {assigned_count} tags to video {video_id}")

    def _merge_tags_intelligently(self, video_id: int, new_tags: List[Dict[str, str]], max_tags: int = 5):
        """Intelligently merge new tags with existing ones, keeping the best combination"""
        video = self.video_service.get_video_by_id(video_id)
        if not video:
            return

        # Get existing tags
        existing_tags = [{"name": tag.name, "color": tag.color, "description": tag.description}
                        for tag in video.tags]

        # Combine and deduplicate tags (case-insensitive)
        all_tags = []
        seen_names = set()

        # Add existing tags first (they have proven relevance)
        for tag in existing_tags:
            tag_name_lower = tag["name"].lower()
            if tag_name_lower not in seen_names:
                all_tags.append(tag)
                seen_names.add(tag_name_lower)

        # Add new tags if they're not duplicates
        for tag in new_tags:
            tag_name_lower = tag["name"].lower()
            if tag_name_lower not in seen_names:
                all_tags.append(tag)
                seen_names.add(tag_name_lower)

        # If we have more tags than the limit, keep the first max_tags
        # (existing tags are prioritized since they were added first)
        final_tags = all_tags[:max_tags]

        # Clear existing tags and assign the final set
        self._clear_video_tags(video_id)
        self._assign_tags_to_video(video_id, final_tags, max_tags)

        print(f"Intelligently merged tags for video {video_id}: kept {len(final_tags)} out of {len(all_tags)} total tags")

    async def reprocess_video_with_merge_async(self, video_id: int):
        """Reprocess a video asynchronously with intelligent tag merging"""
        # Use semaphore to limit concurrent processing operations
        async with self._processing_semaphore:
            try:
                # Update status to processing
                self.video_service.update_processing_status(video_id, "processing", progress=0)

                # Get video
                video = self.video_service.get_video_by_id(video_id)
                if not video:
                    raise Exception(f"Video {video_id} not found")

                # Step 1: Re-transcribe video (0% -> 50%)
                self.video_service.update_processing_status(video_id, "processing", progress=10)
                print(f"Starting re-transcription for video {video_id}")

                raw_transcript, language = await self.transcribe_video(video.file_path)

                self.video_service.update_processing_status(video_id, "processing", progress=50)
                print(f"Re-transcription completed for video {video_id}")

                # Step 2: Refine transcript using LLM (50% -> 70%)
                self.video_service.update_processing_status(video_id, "processing", progress=55)
                print(f"Starting transcript refinement for video {video_id}")

                refined_transcript, refinement_confidence = await self.refine_transcript(
                    raw_transcript, language
                )

                self.video_service.update_processing_status(video_id, "processing", progress=70)
                print(f"Transcript refinement completed for video {video_id} with confidence {refinement_confidence:.2f}")

                # Step 3: Generate new tags from refined transcript (70% -> 85%)
                self.video_service.update_processing_status(video_id, "processing", progress=75)
                print(f"Starting tag generation for video {video_id}")

                suggested_tags = await self.generate_tags_from_transcript(
                    refined_transcript, video.title or video.original_filename
                )

                self.video_service.update_processing_status(video_id, "processing", progress=85)
                print(f"Tag generation completed for video {video_id}")

                # Step 4: Intelligently merge tags (85% -> 95%)
                self.video_service.update_processing_status(video_id, "processing", progress=90)
                self._merge_tags_intelligently(video_id, suggested_tags)

                # Step 5: Update video with refined transcript and status (95% -> 100%)
                self.video_service.update_processing_status_with_refinement(
                    video_id, "completed", refined_transcript, language,
                    raw_transcript, refinement_confidence, progress=100
                )

                print(f"Video {video_id} reprocessing with merge completed successfully")

            except Exception as e:
                print(f"Error reprocessing video {video_id} with merge: {e}")
                self.video_service.update_processing_status(video_id, "failed")
    
    def get_processing_queue_status(self) -> Dict[str, Any]:
        """Get status of processing queue"""
        pending_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "pending"
        ).count()
        
        running_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "running"
        ).count()
        
        completed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "completed"
        ).count()
        
        failed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "failed"
        ).count()
        
        return {
            "pending": pending_jobs,
            "running": running_jobs,
            "completed": completed_jobs,
            "failed": failed_jobs,
            "total": pending_jobs + running_jobs + completed_jobs + failed_jobs
        }
    
    def cleanup_failed_jobs(self):
        """Clean up failed processing jobs"""
        failed_jobs = self.db.query(ProcessingJob).filter(
            ProcessingJob.status == "failed"
        ).all()
        
        for job in failed_jobs:
            # Reset video status to pending for retry
            self.video_service.update_processing_status(job.video_id, "pending")
            
            # Delete failed job
            self.db.delete(job)
        
        self.db.commit()
    
    def get_video_processing_history(self, video_id: int) -> List[ProcessingJob]:
        """Get processing history for a video"""
        return self.db.query(ProcessingJob).filter(
            ProcessingJob.video_id == video_id
        ).order_by(ProcessingJob.id.desc()).all()
