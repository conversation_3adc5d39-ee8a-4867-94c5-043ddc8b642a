"""
Transcript Refinement Service

This service provides LLM-based transcript post-processing to improve the quality
of Whisper-generated transcripts before they are used for tag generation and 
displayed to users.
"""

import asyncio
import requests
import json
import re
from typing import Tuple, Optional
from datetime import datetime


class TranscriptRefinementService:
    """Service for refining transcripts using LLM"""
    
    def __init__(self):
        self.ollama_endpoints = [
            "http://ollama:11434/api/generate",
            "http://localhost:11434/api/generate"
        ]
        self.model_name = "llama3.2:3b"
        self.timeout = 45  # Increased timeout for transcript processing
    
    async def refine_transcript(self, raw_transcript: str, language: str = "auto") -> Tuple[str, float]:
        """
        Refine a transcript using LLM to improve quality.
        
        Args:
            raw_transcript: The original Whisper transcript
            language: The detected language of the transcript
            
        Returns:
            Tuple of (refined_transcript, confidence_score)
        """
        if not raw_transcript or not raw_transcript.strip():
            return raw_transcript, 0.0
        
        try:
            print(f"Starting transcript refinement for {len(raw_transcript)} characters")
            
            # Run refinement in executor to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._refine_transcript_sync,
                raw_transcript,
                language
            )
            
            refined_transcript, confidence = result
            
            if refined_transcript and len(refined_transcript.strip()) > 0:
                print(f"Transcript refinement completed with confidence {confidence:.2f}")
                return refined_transcript, confidence
            else:
                print("Refinement returned empty result, using original")
                return raw_transcript, 0.0
                
        except Exception as e:
            print(f"Transcript refinement failed: {e}")
            return raw_transcript, 0.0
    
    def _refine_transcript_sync(self, raw_transcript: str, language: str) -> Tuple[str, float]:
        """Synchronous transcript refinement method"""
        try:
            # Create language-specific prompt
            prompt = self._create_refinement_prompt(raw_transcript, language)
            
            # Try each Ollama endpoint
            for endpoint in self.ollama_endpoints:
                try:
                    print(f"Trying transcript refinement with Ollama at {endpoint}")
                    
                    response = requests.post(
                        endpoint,
                        json={
                            'model': self.model_name,
                            'prompt': prompt,
                            'stream': False,
                            'options': {
                                'temperature': 0.1,  # Very low temperature for consistency
                                'top_p': 0.9,
                                'num_predict': len(raw_transcript) + 200,  # Allow for expansion
                                'stop': ['\n\n---', 'Human:', 'Assistant:', 'Original:', 'Refined:']
                            }
                        },
                        timeout=self.timeout
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        refined_text = result.get('response', '').strip()
                        
                        if refined_text:
                            # Extract the refined transcript and calculate confidence
                            processed_transcript, confidence = self._process_llm_response(
                                refined_text, raw_transcript
                            )
                            
                            if processed_transcript:
                                return processed_transcript, confidence
                    
                except requests.exceptions.RequestException as e:
                    print(f"Failed to connect to {endpoint}: {e}")
                    continue
            
            print("All Ollama endpoints failed, using original transcript")
            return raw_transcript, 0.0
            
        except Exception as e:
            print(f"Error in transcript refinement: {e}")
            return raw_transcript, 0.0
    
    def _create_refinement_prompt(self, raw_transcript: str, language: str) -> str:
        """Create a language-specific prompt for transcript refinement"""
        
        # Detect language if auto
        if language == "auto" or not language:
            language = self._detect_language_from_content(raw_transcript)
        
        # Language-specific instructions
        if language.lower().startswith('es') or language.lower() == 'spanish':
            language_instruction = """
Instrucciones específicas para español:
- Corrige errores de transcripción comunes en español
- Mejora la puntuación y estructura de oraciones
- Mantén expresiones idiomáticas y regionalismos
- Preserva nombres propios y términos técnicos
"""
        else:
            language_instruction = """
Language-specific instructions for English:
- Fix common English transcription errors
- Improve punctuation and sentence structure
- Maintain idiomatic expressions and colloquialisms
- Preserve proper nouns and technical terms
"""
        
        prompt = f"""You are an expert transcript editor. Your task is to refine and improve the quality of this video transcript while preserving its original meaning and context.

{language_instruction}

IMPORTANT RULES:
1. Fix grammatical errors and improve sentence structure
2. Correct words that may have been misheard or transcribed incorrectly
3. Add proper punctuation and capitalization
4. Maintain the original meaning and context exactly
5. Preserve all proper nouns, names, and technical terms
6. Keep the same speaking style and tone
7. Do not add new information or interpretations
8. Do not remove any important content
9. Return ONLY the refined transcript, no explanations

Original transcript:
{raw_transcript}

Refined transcript:"""
        
        return prompt
    
    def _detect_language_from_content(self, text: str) -> str:
        """Simple language detection based on content"""
        if not text or len(text.strip()) < 10:
            return "english"  # Default to English for short texts

        text_lower = text.lower()

        # Count Spanish-specific words and patterns
        spanish_indicators = [
            'que', 'con', 'para', 'por', 'como', 'pero', 'cuando', 'donde',
            'porque', 'también', 'muy', 'más', 'está', 'son', 'tiene',
            'hacer', 'puede', 'todo', 'año', 'día', 'hola', 'esto', 'vamos',
            'una', 'del', 'las', 'los', 'esta', 'este', 'bien', 'gracias'
        ]

        # Count English-specific words
        english_indicators = [
            'the', 'and', 'that', 'have', 'for', 'not', 'with', 'you',
            'this', 'but', 'his', 'from', 'they', 'she', 'her', 'been',
            'than', 'its', 'who', 'did', 'yes', 'get', 'may', 'him'
        ]

        # Count occurrences
        spanish_count = sum(1 for word in spanish_indicators if f' {word} ' in f' {text_lower} ')
        english_count = sum(1 for word in english_indicators if f' {word} ' in f' {text_lower} ')

        # Determine language based on counts
        if spanish_count > english_count and spanish_count >= 2:
            return "spanish"
        else:
            return "english"
    
    def _process_llm_response(self, llm_response: str, original_transcript: str) -> Tuple[str, float]:
        """Process the LLM response and calculate confidence score"""
        try:
            # Clean up the response
            refined = llm_response.strip()
            
            # Remove any potential prompt echoing or explanations
            refined = self._clean_llm_output(refined)
            
            if not refined or len(refined.strip()) < 10:
                return original_transcript, 0.0
            
            # Calculate confidence based on various factors
            confidence = self._calculate_refinement_confidence(original_transcript, refined)
            
            return refined, confidence
            
        except Exception as e:
            print(f"Error processing LLM response: {e}")
            return original_transcript, 0.0
    
    def _clean_llm_output(self, text: str) -> str:
        """Clean up LLM output to extract just the refined transcript"""
        # Remove common LLM artifacts
        text = re.sub(r'^(Refined transcript:|Here is the refined transcript:|The refined transcript is:)', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^(Original:|Refined:)', '', text, flags=re.IGNORECASE | re.MULTILINE)
        
        # Remove explanatory text at the end
        text = re.sub(r'\n\n(Note:|Explanation:|Changes made:).*$', '', text, flags=re.IGNORECASE | re.DOTALL)
        
        # Clean up extra whitespace
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)  # Multiple newlines to double
        text = text.strip()
        
        return text
    
    def _calculate_refinement_confidence(self, original: str, refined: str) -> float:
        """Calculate confidence score for the refinement"""
        try:
            # Basic checks
            if not refined or len(refined.strip()) < 10:
                return 0.0
            
            # Length similarity (refined should be similar length, maybe slightly longer)
            length_ratio = len(refined) / len(original) if len(original) > 0 else 0
            length_score = 1.0 if 0.8 <= length_ratio <= 1.5 else max(0.0, 1.0 - abs(length_ratio - 1.0))
            
            # Content preservation (check if key words are preserved)
            original_words = set(original.lower().split())
            refined_words = set(refined.lower().split())
            
            if len(original_words) > 0:
                preserved_ratio = len(original_words.intersection(refined_words)) / len(original_words)
            else:
                preserved_ratio = 0.0
            
            # Quality indicators (proper capitalization, punctuation)
            quality_score = 0.0
            if refined != refined.lower():  # Has capitalization
                quality_score += 0.3
            if any(punct in refined for punct in '.!?'):  # Has sentence endings
                quality_score += 0.3
            if any(punct in refined for punct in ',:;'):  # Has internal punctuation
                quality_score += 0.2
            if refined[0].isupper() if refined else False:  # Starts with capital
                quality_score += 0.2
            
            # Combine scores
            confidence = (length_score * 0.3 + preserved_ratio * 0.5 + quality_score * 0.2)
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            print(f"Error calculating confidence: {e}")
            return 0.0
