from sqlalchemy.orm import Session
from typing import Optional, List
from models.database import Recipe, Video
from models.schemas import RecipeCreate, RecipeUpdate, RecipeResponse, IngredientBase, InstructionStep
from services.sharing_service import SharingService
import json


class RecipeService:
    """Service for managing recipe data"""

    def __init__(self, db: Session):
        self.db = db

    def _get_accessible_video_ids(self, user_id: int) -> List[int]:
        """Get list of video IDs that the user has access to (owns or has been shared with)"""
        # Get videos owned by the user
        owned_videos = self.db.query(Video.id).filter(Video.user_id == user_id).all()
        owned_video_ids = [video.id for video in owned_videos]

        # Get videos shared with the user
        shared_video_ids = []
        all_videos = self.db.query(Video).all()
        for video in all_videos:
            if video.id not in owned_video_ids:
                permission = SharingService.get_user_permission(self.db, video.id, user_id)
                if permission:
                    shared_video_ids.append(video.id)

        return owned_video_ids + shared_video_ids
    
    def get_recipe_by_video_id(self, video_id: int) -> Optional[Recipe]:
        """Get recipe for a specific video"""
        return self.db.query(Recipe).filter(Recipe.video_id == video_id).first()
    
    def get_recipe_by_id(self, recipe_id: int) -> Optional[Recipe]:
        """Get recipe by ID"""
        return self.db.query(Recipe).filter(Recipe.id == recipe_id).first()
    
    def create_recipe(self, recipe_data: RecipeCreate) -> Recipe:
        """Create a new recipe"""
        try:
            # Convert ingredients and instructions to JSON
            ingredients_json = [ing.dict() for ing in recipe_data.ingredients] if recipe_data.ingredients else []
            instructions_json = [inst.dict() for inst in recipe_data.instructions] if recipe_data.instructions else []
            
            recipe = Recipe(
                video_id=recipe_data.video_id,
                title=recipe_data.title,
                description=recipe_data.description,
                ingredients=ingredients_json,
                instructions=instructions_json,
                prep_time=recipe_data.prep_time,
                cook_time=recipe_data.cook_time,
                total_time=recipe_data.total_time,
                servings=recipe_data.servings,
                difficulty=recipe_data.difficulty,
                cuisine_type=recipe_data.cuisine_type,
                extraction_confidence=recipe_data.extraction_confidence
            )
            
            self.db.add(recipe)
            self.db.commit()
            self.db.refresh(recipe)
            return recipe
            
        except Exception as e:
            print(f"Error creating recipe: {e}")
            self.db.rollback()
            raise
    
    def update_recipe(self, recipe_id: int, recipe_data: RecipeUpdate) -> Optional[Recipe]:
        """Update an existing recipe"""
        try:
            recipe = self.get_recipe_by_id(recipe_id)
            if not recipe:
                return None
            
            # Update fields if provided
            if recipe_data.title is not None:
                recipe.title = recipe_data.title
            if recipe_data.description is not None:
                recipe.description = recipe_data.description
            if recipe_data.prep_time is not None:
                recipe.prep_time = recipe_data.prep_time
            if recipe_data.cook_time is not None:
                recipe.cook_time = recipe_data.cook_time
            if recipe_data.total_time is not None:
                recipe.total_time = recipe_data.total_time
            if recipe_data.servings is not None:
                recipe.servings = recipe_data.servings
            if recipe_data.difficulty is not None:
                recipe.difficulty = recipe_data.difficulty
            if recipe_data.cuisine_type is not None:
                recipe.cuisine_type = recipe_data.cuisine_type
            
            # Update ingredients if provided
            if recipe_data.ingredients is not None:
                ingredients_json = [ing.dict() for ing in recipe_data.ingredients]
                recipe.ingredients = ingredients_json
            
            # Update instructions if provided
            if recipe_data.instructions is not None:
                instructions_json = [inst.dict() for inst in recipe_data.instructions]
                recipe.instructions = instructions_json
            
            self.db.commit()
            self.db.refresh(recipe)
            return recipe
            
        except Exception as e:
            print(f"Error updating recipe: {e}")
            self.db.rollback()
            return None
    
    def delete_recipe(self, recipe_id: int) -> bool:
        """Delete a recipe"""
        try:
            recipe = self.get_recipe_by_id(recipe_id)
            if not recipe:
                return False
            
            self.db.delete(recipe)
            self.db.commit()
            return True
            
        except Exception as e:
            print(f"Error deleting recipe: {e}")
            self.db.rollback()
            return False
    
    def get_recipes_by_cuisine(self, cuisine_type: str, limit: int = 20, user_id: Optional[int] = None) -> List[Recipe]:
        """Get recipes by cuisine type filtered by user permissions"""
        if user_id is None:
            return []
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        return self.db.query(Recipe).filter(
            Recipe.video_id.in_(accessible_video_ids),
            Recipe.cuisine_type.ilike(f"%{cuisine_type}%")
        ).limit(limit).all()

    def get_recipes_by_difficulty(self, difficulty: str, limit: int = 20, user_id: Optional[int] = None) -> List[Recipe]:
        """Get recipes by difficulty level filtered by user permissions"""
        if user_id is None:
            return []
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        return self.db.query(Recipe).filter(
            Recipe.video_id.in_(accessible_video_ids),
            Recipe.difficulty.ilike(f"%{difficulty}%")
        ).limit(limit).all()

    def search_recipes(self, query: str, limit: int = 20, user_id: Optional[int] = None) -> List[Recipe]:
        """Search recipes by title, description, or ingredients filtered by user permissions"""
        if user_id is None:
            return []
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        search_term = f"%{query}%"

        # Search in title, description, and ingredients JSON
        from sqlalchemy import func
        return self.db.query(Recipe).filter(
            Recipe.video_id.in_(accessible_video_ids)
        ).filter(
            Recipe.title.ilike(search_term) |
            Recipe.description.ilike(search_term) |
            func.json_extract(Recipe.ingredients, '$').like(search_term)
        ).limit(limit).all()

    def get_all_recipes(self, skip: int = 0, limit: int = 100, user_id: Optional[int] = None) -> List[Recipe]:
        """Get all recipes with pagination filtered by user permissions"""
        if user_id is None:
            return []
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        return self.db.query(Recipe).filter(
            Recipe.video_id.in_(accessible_video_ids)
        ).offset(skip).limit(limit).all()
    
    def convert_to_response(self, recipe: Recipe) -> RecipeResponse:
        """Convert Recipe model to RecipeResponse schema"""
        try:
            # Convert JSON back to Pydantic models
            ingredients = []
            if recipe.ingredients:
                for ing_data in recipe.ingredients:
                    ingredient = IngredientBase(**ing_data)
                    ingredients.append(ingredient)
            
            instructions = []
            if recipe.instructions:
                for inst_data in recipe.instructions:
                    instruction = InstructionStep(**inst_data)
                    instructions.append(instruction)
            
            return RecipeResponse(
                id=recipe.id,
                video_id=recipe.video_id,
                title=recipe.title,
                description=recipe.description,
                ingredients=ingredients,
                instructions=instructions,
                prep_time=recipe.prep_time,
                cook_time=recipe.cook_time,
                total_time=recipe.total_time,
                servings=recipe.servings,
                difficulty=recipe.difficulty,
                cuisine_type=recipe.cuisine_type,
                extracted_at=recipe.extracted_at,
                extraction_confidence=recipe.extraction_confidence
            )
            
        except Exception as e:
            print(f"Error converting recipe to response: {e}")
            # Return basic response if conversion fails
            return RecipeResponse(
                id=recipe.id,
                video_id=recipe.video_id,
                title=recipe.title or "Unknown Recipe",
                description=recipe.description,
                ingredients=[],
                instructions=[],
                extracted_at=recipe.extracted_at,
                extraction_confidence=recipe.extraction_confidence
            )
    
    def get_recipe_stats(self) -> dict:
        """Get recipe statistics"""
        total_recipes = self.db.query(Recipe).count()
        
        # Count by difficulty
        easy_count = self.db.query(Recipe).filter(Recipe.difficulty == "Easy").count()
        medium_count = self.db.query(Recipe).filter(Recipe.difficulty == "Medium").count()
        hard_count = self.db.query(Recipe).filter(Recipe.difficulty == "Hard").count()
        
        # Count by cuisine (top 5)
        cuisine_stats = self.db.query(Recipe.cuisine_type).filter(
            Recipe.cuisine_type.isnot(None)
        ).distinct().all()
        
        # Calculate average confidence
        from sqlalchemy import func
        avg_confidence = self.db.query(func.avg(Recipe.extraction_confidence)).filter(
            Recipe.extraction_confidence.isnot(None)
        ).scalar() or 0.0

        return {
            "total_recipes": total_recipes,
            "difficulty_breakdown": {
                "easy": easy_count,
                "medium": medium_count,
                "hard": hard_count
            },
            "cuisine_types": len(cuisine_stats),
            "avg_confidence": float(avg_confidence)
        }
