from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict

from models.database import Video, Tag, video_tags
from models.schemas import AnalyticsResponse
from services.sharing_service import SharingService

class AnalyticsService:
    def __init__(self, db: Session):
        self.db = db

    def _get_accessible_video_ids(self, user_id: int) -> List[int]:
        """Get list of video IDs that the user has access to (owns or has been shared with)"""
        # Get videos owned by the user
        owned_videos = self.db.query(Video.id).filter(Video.user_id == user_id).all()
        owned_video_ids = [video.id for video in owned_videos]

        # Get videos shared with the user
        shared_video_ids = []
        all_videos = self.db.query(Video).all()
        for video in all_videos:
            if video.id not in owned_video_ids:
                permission = SharingService.get_user_permission(self.db, video.id, user_id)
                if permission:
                    shared_video_ids.append(video.id)

        return owned_video_ids + shared_video_ids
    
    def get_analytics(self, start_date: datetime, end_date: datetime, user_id: Optional[int] = None) -> AnalyticsResponse:
        """Get comprehensive analytics data filtered by user permissions"""
        return AnalyticsResponse(
            total_videos=self.get_total_videos(user_id),
            total_tags=self.get_total_tags(user_id),
            total_duration=self.get_total_duration(user_id),
            processed_videos=self.get_processed_videos_count(user_id),
            pending_videos=self.get_pending_videos_count(user_id),
            top_tags=self.get_top_tags_data(10, user_id),
            language_distribution=self.get_language_distribution(user_id),
            upload_timeline=self.get_upload_timeline(start_date, end_date, user_id),
            duration_distribution=self.get_duration_distribution(user_id)
        )
    
    def get_summary(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Get basic analytics summary filtered by user permissions"""
        return {
            "total_videos": self.get_total_videos(user_id),
            "total_tags": self.get_total_tags(user_id),
            "processed_videos": self.get_processed_videos_count(user_id),
            "pending_videos": self.get_pending_videos_count(user_id),
            "total_duration": self.get_total_duration(user_id),
            "average_duration": self.get_average_duration(user_id)
        }
    
    def get_total_videos(self, user_id: Optional[int] = None) -> int:
        """Get total number of videos accessible to user"""
        if user_id is None:
            return 0
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        return len(accessible_video_ids)

    def get_total_tags(self, user_id: Optional[int] = None) -> int:
        """Get total number of tags from accessible videos"""
        if user_id is None:
            return 0
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return 0
        return self.db.query(Tag).join(video_tags).filter(
            video_tags.c.video_id.in_(accessible_video_ids)
        ).distinct().count()

    def get_processed_videos_count(self, user_id: Optional[int] = None) -> int:
        """Get number of processed videos accessible to user"""
        if user_id is None:
            return 0
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return 0
        return self.db.query(Video).filter(
            Video.id.in_(accessible_video_ids),
            Video.processed == True
        ).count()

    def get_pending_videos_count(self, user_id: Optional[int] = None) -> int:
        """Get number of pending videos accessible to user"""
        if user_id is None:
            return 0
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return 0
        return self.db.query(Video).filter(
            Video.id.in_(accessible_video_ids),
            Video.processed == False
        ).count()

    def get_total_duration(self, user_id: Optional[int] = None) -> float:
        """Get total duration of accessible videos in seconds"""
        if user_id is None:
            return 0.0
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return 0.0
        result = self.db.query(func.sum(Video.duration)).filter(
            Video.id.in_(accessible_video_ids)
        ).scalar()
        return result or 0.0

    def get_average_duration(self, user_id: Optional[int] = None) -> float:
        """Get average duration of accessible videos in seconds"""
        if user_id is None:
            return 0.0
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return 0.0
        result = self.db.query(func.avg(Video.duration)).filter(
            Video.id.in_(accessible_video_ids)
        ).scalar()
        return result or 0.0
    
    def get_top_tags(self, limit: int = 10, user_id: Optional[int] = None) -> List[Tag]:
        """Get top tags by usage count from accessible videos"""
        if user_id is None:
            return []
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        # Query tags that are associated with accessible videos and calculate usage count
        tag_usage = self.db.query(
            Tag,
            func.count(video_tags.c.video_id).label('accessible_usage_count')
        ).join(video_tags).filter(
            video_tags.c.video_id.in_(accessible_video_ids)
        ).group_by(Tag.id).having(
            func.count(video_tags.c.video_id) > 0
        ).order_by(desc(func.count(video_tags.c.video_id))).limit(limit).all()

        return [tag for tag, usage_count in tag_usage]

    def get_top_tags_data(self, limit: int = 10, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get top tags data for analytics from accessible videos"""
        if user_id is None:
            return []
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        # Query tags that are associated with accessible videos and calculate usage count
        tag_usage = self.db.query(
            Tag.id,
            Tag.name,
            Tag.color,
            Tag.description,
            func.count(video_tags.c.video_id).label('accessible_usage_count')
        ).join(video_tags).filter(
            video_tags.c.video_id.in_(accessible_video_ids)
        ).group_by(Tag.id, Tag.name, Tag.color, Tag.description).having(
            func.count(video_tags.c.video_id) > 0
        ).order_by(desc(func.count(video_tags.c.video_id))).limit(limit).all()

        return [
            {
                "id": tag_id,
                "name": name,
                "color": color,
                "usage_count": usage_count,
                "description": description
            }
            for tag_id, name, color, description, usage_count in tag_usage
        ]
    
    def get_language_distribution(self, user_id: Optional[int] = None) -> Dict[str, int]:
        """Get distribution of transcript languages from accessible videos"""
        if user_id is None:
            return {}
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return {}

        results = self.db.query(
            Video.transcript_language,
            func.count(Video.id)
        ).filter(
            Video.id.in_(accessible_video_ids),
            Video.transcript_language.isnot(None)
        ).group_by(Video.transcript_language).all()

        distribution = {}
        for language, count in results:
            distribution[language or "unknown"] = count

        return distribution
    
    def get_upload_timeline(self, start_date: datetime, end_date: datetime, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get video upload timeline from accessible videos"""
        if user_id is None:
            return []
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return []

        # Group by date
        results = self.db.query(
            func.date(Video.upload_date).label('date'),
            func.count(Video.id).label('count')
        ).filter(
            and_(
                Video.id.in_(accessible_video_ids),
                Video.upload_date >= start_date,
                Video.upload_date <= end_date
            )
        ).group_by(func.date(Video.upload_date)).order_by('date').all()

        timeline = []
        for date, count in results:
            timeline.append({
                "date": str(date) if date else None,
                "count": count
            })

        return timeline
    
    def get_duration_distribution(self, user_id: Optional[int] = None) -> Dict[str, int]:
        """Get video duration distribution in buckets from accessible videos"""
        if user_id is None:
            return {}
        accessible_video_ids = self._get_accessible_video_ids(user_id)
        if not accessible_video_ids:
            return {}

        videos = self.db.query(Video.duration).filter(
            Video.id.in_(accessible_video_ids),
            Video.duration.isnot(None)
        ).all()

        # Define duration buckets (in seconds)
        buckets = {
            "0-30s": 0,
            "30s-1m": 0,
            "1-2m": 0,
            "2-5m": 0,
            "5-10m": 0,
            "10m+": 0
        }

        for (duration,) in videos:
            if duration <= 30:
                buckets["0-30s"] += 1
            elif duration <= 60:
                buckets["30s-1m"] += 1
            elif duration <= 120:
                buckets["1-2m"] += 1
            elif duration <= 300:
                buckets["2-5m"] += 1
            elif duration <= 600:
                buckets["5-10m"] += 1
            else:
                buckets["10m+"] += 1

        return buckets
    
    def get_processing_status(self) -> Dict[str, int]:
        """Get video processing status breakdown"""
        results = self.db.query(
            Video.processing_status,
            func.count(Video.id)
        ).group_by(Video.processing_status).all()
        
        status_counts = {}
        for status, count in results:
            status_counts[status] = count
        
        return status_counts
    
    def get_monthly_stats(self, months: int = 12) -> List[Dict[str, Any]]:
        """Get monthly upload statistics"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        results = self.db.query(
            func.extract('year', Video.upload_date).label('year'),
            func.extract('month', Video.upload_date).label('month'),
            func.count(Video.id).label('count'),
            func.sum(Video.duration).label('total_duration')
        ).filter(
            Video.upload_date >= start_date
        ).group_by(
            func.extract('year', Video.upload_date),
            func.extract('month', Video.upload_date)
        ).order_by('year', 'month').all()
        
        monthly_stats = []
        for year, month, count, total_duration in results:
            monthly_stats.append({
                "year": int(year),
                "month": int(month),
                "video_count": count,
                "total_duration": total_duration or 0,
                "average_duration": (total_duration / count) if count > 0 and total_duration else 0
            })
        
        return monthly_stats
    
    def get_tag_usage_trends(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get tag usage trends over time"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get videos uploaded in the time period with their tags
        videos_with_tags = self.db.query(Video).filter(
            Video.upload_date >= start_date
        ).all()
        
        # Count tag usage by day
        daily_tag_usage = defaultdict(lambda: defaultdict(int))
        
        for video in videos_with_tags:
            date_key = video.upload_date.date().isoformat()
            for tag in video.tags:
                daily_tag_usage[date_key][tag.name] += 1
        
        # Convert to list format
        trends = []
        for date, tag_counts in daily_tag_usage.items():
            for tag_name, count in tag_counts.items():
                trends.append({
                    "date": date,
                    "tag_name": tag_name,
                    "usage_count": count
                })
        
        return sorted(trends, key=lambda x: x["date"])
