"""
Security utilities for input sanitization and validation.
"""

import re
import html
from typing import Optional


def sanitize_html(content: Optional[str]) -> Optional[str]:
    """
    Sanitize HTML content by removing all HTML tags and escaping special characters.
    
    Args:
        content: The content to sanitize
        
    Returns:
        Sanitized content safe for display
    """
    if not content:
        return content
    
    # Remove HTML tags
    content = re.sub(r'<[^>]+>', '', content)
    
    # Escape HTML entities
    content = html.escape(content)
    
    # Remove potentially dangerous characters
    content = re.sub(r'[<>"\']', '', content)
    
    # Limit length to prevent DoS
    if len(content) > 10000:
        content = content[:10000] + "..."
    
    return content.strip()


def sanitize_filename(filename: Optional[str]) -> Optional[str]:
    """
    Sanitize filename to prevent path traversal attacks.
    
    Args:
        filename: The filename to sanitize
        
    Returns:
        Sanitized filename safe for file operations
    """
    if not filename:
        return filename
    
    # Remove path separators and dangerous characters
    filename = re.sub(r'[/\\:*?"<>|]', '', filename)
    
    # Remove leading/trailing dots and spaces
    filename = filename.strip('. ')
    
    # Limit length
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        filename = name[:250] + ('.' + ext if ext else '')
    
    return filename


def validate_video_title(title: Optional[str]) -> Optional[str]:
    """
    Validate and sanitize video title.
    
    Args:
        title: The title to validate
        
    Returns:
        Sanitized title or None if invalid
    """
    if not title:
        return title
    
    # Sanitize HTML
    title = sanitize_html(title)
    
    # Check length
    if len(title) > 200:
        title = title[:200] + "..."
    
    return title


def validate_tag_name(name: Optional[str]) -> Optional[str]:
    """
    Validate and sanitize tag name.
    
    Args:
        name: The tag name to validate
        
    Returns:
        Sanitized tag name or None if invalid
    """
    if not name:
        return name
    
    # Sanitize HTML
    name = sanitize_html(name)
    
    # Remove special characters except spaces, hyphens, and underscores
    name = re.sub(r'[^a-zA-Z0-9\s\-_]', '', name)
    
    # Normalize whitespace
    name = re.sub(r'\s+', ' ', name).strip()
    
    # Check length
    if len(name) > 50:
        name = name[:50]
    
    return name


def validate_description(description: Optional[str]) -> Optional[str]:
    """
    Validate and sanitize description text.
    
    Args:
        description: The description to validate
        
    Returns:
        Sanitized description or None if invalid
    """
    if not description:
        return description
    
    # Sanitize HTML
    description = sanitize_html(description)
    
    # Check length
    if len(description) > 1000:
        description = description[:1000] + "..."
    
    return description


def validate_transcript(transcript: Optional[str]) -> Optional[str]:
    """
    Validate and sanitize transcript content.
    
    Args:
        transcript: The transcript to validate
        
    Returns:
        Sanitized transcript or None if invalid
    """
    if not transcript:
        return transcript
    
    # For transcripts, we're more lenient but still remove dangerous content
    # Remove script tags and other dangerous HTML
    transcript = re.sub(r'<script[^>]*>.*?</script>', '', transcript, flags=re.IGNORECASE | re.DOTALL)
    transcript = re.sub(r'<iframe[^>]*>.*?</iframe>', '', transcript, flags=re.IGNORECASE | re.DOTALL)
    transcript = re.sub(r'<object[^>]*>.*?</object>', '', transcript, flags=re.IGNORECASE | re.DOTALL)
    transcript = re.sub(r'<embed[^>]*>', '', transcript, flags=re.IGNORECASE)
    
    # Remove javascript: and data: URLs
    transcript = re.sub(r'javascript:', '', transcript, flags=re.IGNORECASE)
    transcript = re.sub(r'data:', '', transcript, flags=re.IGNORECASE)
    
    # Limit length
    if len(transcript) > 50000:
        transcript = transcript[:50000] + "..."
    
    return transcript


def validate_color_hex(color: Optional[str]) -> Optional[str]:
    """
    Validate hex color code.
    
    Args:
        color: The color hex code to validate
        
    Returns:
        Valid hex color or None if invalid
    """
    if not color:
        return color
    
    # Check if it's a valid hex color
    if re.match(r'^#[0-9A-Fa-f]{6}$', color):
        return color.upper()
    
    return None


def is_safe_url(url: Optional[str]) -> bool:
    """
    Check if URL is safe (no javascript:, data:, etc.).
    
    Args:
        url: The URL to check
        
    Returns:
        True if URL is safe, False otherwise
    """
    if not url:
        return True
    
    # Check for dangerous protocols
    dangerous_protocols = ['javascript:', 'data:', 'vbscript:', 'file:', 'ftp:']
    url_lower = url.lower().strip()
    
    for protocol in dangerous_protocols:
        if url_lower.startswith(protocol):
            return False
    
    return True
