"""
Language utilities for multilingual tag generation and content analysis.
Provides comprehensive stop word filtering and language-specific processing.
"""

import re
from typing import Set, List, Tuple, Dict
from collections import Counter


class LanguageProcessor:
    """Handles language detection and language-specific text processing"""
    
    def __init__(self):
        self.spanish_stop_words = self._get_spanish_stop_words()
        self.english_stop_words = self._get_english_stop_words()
        self.spanish_indicators = self._get_spanish_indicators()
        self.english_indicators = self._get_english_indicators()
    
    def _get_spanish_stop_words(self) -> Set[str]:
        """Comprehensive Spanish stop words list"""
        return {
            # Articles
            'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas',
            
            # Prepositions
            'a', 'ante', 'bajo', 'con', 'contra', 'de', 'desde', 'durante', 'en', 'entre',
            'hacia', 'hasta', 'para', 'por', 'según', 'sin', 'sobre', 'tras',
            
            # Pronouns
            'yo', 'tú', 'él', 'ella', 'nosotros', 'nosotras', 'vosotros', 'vosotras',
            'ellos', 'ellas', 'me', 'te', 'se', 'nos', 'os', 'le', 'les', 'lo', 'la',
            'los', 'las', 'mi', 'tu', 'su', 'nuestro', 'nuestra', 'vuestro', 'vuestra',
            'este', 'esta', 'estos', 'estas', 'ese', 'esa', 'esos', 'esas',
            'aquel', 'aquella', 'aquellos', 'aquellas', 'esto', 'eso', 'aquello',
            
            # Conjunctions
            'y', 'e', 'o', 'u', 'pero', 'mas', 'sino', 'que', 'si', 'como', 'cuando',
            'donde', 'mientras', 'aunque', 'porque', 'pues', 'luego', 'entonces',
            
            # Adverbs
            'no', 'sí', 'también', 'tampoco', 'muy', 'más', 'menos', 'tan', 'tanto',
            'mucho', 'poco', 'bastante', 'demasiado', 'aquí', 'ahí', 'allí', 'acá',
            'allá', 'donde', 'cuando', 'como', 'así', 'bien', 'mal', 'mejor', 'peor',
            'antes', 'después', 'ahora', 'luego', 'siempre', 'nunca', 'jamás',
            'todavía', 'ya', 'aún', 'recién', 'pronto', 'tarde', 'temprano',
            
            # Common verbs (conjugated forms)
            'ser', 'estar', 'tener', 'hacer', 'decir', 'dar', 'ver', 'saber', 'ir',
            'venir', 'poder', 'querer', 'deber', 'haber', 'poner', 'salir', 'llegar',
            'pasar', 'quedar', 'seguir', 'llevar', 'traer', 'volver', 'empezar',
            'terminar', 'acabar', 'conseguir', 'lograr', 'intentar', 'tratar',
            'es', 'son', 'está', 'están', 'fue', 'fueron', 'era', 'eran',
            'tiene', 'tienen', 'tuvo', 'tuvieron', 'tenía', 'tenían',
            'hace', 'hacen', 'hizo', 'hicieron', 'hacía', 'hacían',
            'dice', 'dicen', 'dijo', 'dijeron', 'decía', 'decían',
            'va', 'van', 'fue', 'fueron', 'iba', 'iban',
            'puede', 'pueden', 'pudo', 'pudieron', 'podía', 'podían',
            'quiere', 'quieren', 'quiso', 'quisieron', 'quería', 'querían',
            'debe', 'deben', 'debió', 'debieron', 'debía', 'debían',
            'pone', 'ponen', 'puso', 'pusieron', 'ponía', 'ponían',
            'sale', 'salen', 'salió', 'salieron', 'salía', 'salían',
            'llega', 'llegan', 'llegó', 'llegaron', 'llegaba', 'llegaban',
            'pasa', 'pasan', 'pasó', 'pasaron', 'pasaba', 'pasaban',
            'queda', 'quedan', 'quedó', 'quedaron', 'quedaba', 'quedaban',
            'sigue', 'siguen', 'siguió', 'siguieron', 'seguía', 'seguían',
            'lleva', 'llevan', 'llevó', 'llevaron', 'llevaba', 'llevaban',
            'trae', 'traen', 'trajo', 'trajeron', 'traía', 'traían',
            'vuelve', 'vuelven', 'volvió', 'volvieron', 'volvía', 'volvían',
            
            # Common words that appear in transcripts but aren't meaningful tags
            'cosa', 'cosas', 'algo', 'nada', 'todo', 'todos', 'toda', 'todas',
            'vez', 'veces', 'momento', 'momentos', 'tiempo', 'tiempos',
            'día', 'días', 'año', 'años', 'mes', 'meses', 'semana', 'semanas',
            'hora', 'horas', 'minuto', 'minutos', 'segundo', 'segundos',
            'parte', 'partes', 'lado', 'lados', 'lugar', 'lugares',
            'forma', 'formas', 'manera', 'maneras', 'modo', 'modos',
            'tipo', 'tipos', 'clase', 'clases', 'ejemplo', 'ejemplos',
            'caso', 'casos', 'problema', 'problemas', 'solución', 'soluciones',
            'idea', 'ideas', 'punto', 'puntos', 'tema', 'temas',
            'video', 'videos', 'canal', 'canales', 'contenido', 'contenidos',
            
            # Filler words and expressions
            'bueno', 'bien', 'vale', 'okay', 'ok', 'eh', 'ah', 'oh',
            'claro', 'obvio', 'obviamente', 'exacto', 'exactamente',
            'perfecto', 'genial', 'fantástico', 'increíble', 'impresionante',
            'simple', 'fácil', 'difícil', 'complicado', 'básico', 'avanzado',
            'importante', 'necesario', 'posible', 'imposible', 'probable',
            'seguro', 'cierto', 'verdad', 'mentira', 'correcto', 'incorrecto',
            
            # Common verbs that don't add semantic value
            'decidí', 'decidir', 'decidió', 'decidieron', 'decidía', 'decidían',
            'mandamos', 'mandar', 'mandó', 'mandaron', 'mandaba', 'mandaban',
            'vamos', 'voy', 'vas', 'vais', 'ven', 'vengo', 'vienes', 'viene',
            'venimos', 'venís', 'vienen', 'vine', 'viniste', 'vino', 'vinimos',
            'vinisteis', 'vinieron', 'venía', 'venías', 'veníamos', 'veníais', 'venían'
        }
    
    def _get_english_stop_words(self) -> Set[str]:
        """Comprehensive English stop words list"""
        return {
            # Articles
            'a', 'an', 'the',
            
            # Prepositions
            'about', 'above', 'across', 'after', 'against', 'along', 'among', 'around',
            'at', 'before', 'behind', 'below', 'beneath', 'beside', 'between', 'beyond',
            'by', 'down', 'during', 'except', 'for', 'from', 'in', 'inside', 'into',
            'like', 'near', 'of', 'off', 'on', 'outside', 'over', 'since', 'through',
            'throughout', 'till', 'to', 'toward', 'under', 'until', 'up', 'upon',
            'with', 'within', 'without',
            
            # Pronouns
            'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your',
            'yours', 'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she',
            'her', 'hers', 'herself', 'it', 'its', 'itself', 'they', 'them', 'their',
            'theirs', 'themselves', 'what', 'which', 'who', 'whom', 'this', 'that',
            'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing',
            
            # Conjunctions
            'and', 'but', 'or', 'nor', 'for', 'yet', 'so', 'because', 'since', 'unless',
            'while', 'where', 'when', 'why', 'how', 'if', 'than', 'that', 'though',
            'although', 'even', 'whether',
            
            # Adverbs
            'not', 'no', 'yes', 'very', 'too', 'so', 'just', 'now', 'then', 'here',
            'there', 'where', 'when', 'why', 'how', 'all', 'any', 'both', 'each',
            'few', 'more', 'most', 'other', 'some', 'such', 'only', 'own', 'same',
            'than', 'too', 'very', 'can', 'will', 'just', 'should', 'now', 'also',
            'well', 'back', 'still', 'way', 'even', 'new', 'want', 'because', 'good',
            'each', 'those', 'people', 'mr', 'one', 'show', 'had', 'his', 'your',
            'its', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'if',
            'up', 'out', 'many', 'then', 'them', 'these', 'so', 'some', 'her', 'would',
            'make', 'like', 'into', 'him', 'has', 'two', 'more', 'go', 'no', 'way',
            'could', 'my', 'than', 'first', 'been', 'call', 'who', 'oil', 'sit',
            'now', 'find', 'long', 'down', 'day', 'did', 'get', 'come', 'made',
            'may', 'part',
            
            # Common verbs that don't add semantic value
            'get', 'got', 'getting', 'gets', 'give', 'gave', 'given', 'giving', 'gives',
            'go', 'goes', 'going', 'went', 'gone', 'make', 'makes', 'making', 'made',
            'take', 'takes', 'taking', 'took', 'taken', 'come', 'comes', 'coming',
            'came', 'put', 'puts', 'putting', 'see', 'sees', 'seeing', 'saw', 'seen',
            'know', 'knows', 'knowing', 'knew', 'known', 'think', 'thinks', 'thinking',
            'thought', 'look', 'looks', 'looking', 'looked', 'use', 'uses', 'using',
            'used', 'find', 'finds', 'finding', 'found', 'tell', 'tells', 'telling',
            'told', 'ask', 'asks', 'asking', 'asked', 'work', 'works', 'working',
            'worked', 'seem', 'seems', 'seeming', 'seemed', 'feel', 'feels', 'feeling',
            'felt', 'try', 'tries', 'trying', 'tried', 'leave', 'leaves', 'leaving',
            'left', 'call', 'calls', 'calling', 'called',
            
            # Filler words and expressions
            'well', 'okay', 'ok', 'yeah', 'yes', 'yep', 'nope', 'uh', 'um', 'ah',
            'oh', 'hey', 'hi', 'hello', 'bye', 'goodbye', 'thanks', 'thank',
            'please', 'sorry', 'excuse', 'actually', 'basically', 'literally',
            'obviously', 'definitely', 'probably', 'maybe', 'perhaps', 'really',
            'truly', 'certainly', 'absolutely', 'exactly', 'perfectly', 'completely',
            'totally', 'quite', 'rather', 'pretty', 'fairly', 'somewhat', 'kind',
            'sort', 'type', 'thing', 'things', 'stuff', 'something', 'anything',
            'nothing', 'everything', 'someone', 'anyone', 'everyone', 'nobody',
            'somebody', 'anybody', 'everybody',
            
            # Video/content related generic words
            'video', 'videos', 'content', 'channel', 'subscribe', 'like', 'comment',
            'share', 'watch', 'viewer', 'viewers', 'episode', 'show', 'series'
        }
    
    def _get_spanish_indicators(self) -> Set[str]:
        """Words that strongly indicate Spanish content"""
        return {
            'que', 'para', 'con', 'una', 'los', 'las', 'del', 'como', 'esto', 'esta',
            'este', 'pero', 'porque', 'también', 'muy', 'más', 'está', 'son', 'tiene',
            'hacer', 'puede', 'todo', 'año', 'día', 'hola', 'vamos', 'bien', 'gracias',
            'donde', 'cuando', 'mientras', 'durante', 'después', 'antes', 'ahora',
            'entonces', 'siempre', 'nunca', 'aquí', 'ahí', 'allí'
        }
    
    def _get_english_indicators(self) -> Set[str]:
        """Words that strongly indicate English content"""
        return {
            'the', 'and', 'that', 'have', 'for', 'not', 'with', 'you', 'this',
            'but', 'his', 'from', 'they', 'she', 'her', 'been', 'than', 'its',
            'who', 'did', 'yes', 'get', 'may', 'him', 'would', 'could', 'should',
            'will', 'can', 'about', 'what', 'when', 'where', 'why', 'how'
        }
    
    def detect_language(self, text: str) -> str:
        """
        Detect if text is primarily Spanish or English.
        
        Args:
            text: Text to analyze
            
        Returns:
            'spanish' or 'english'
        """
        if not text or len(text.strip()) < 10:
            return "english"  # Default to English for short texts
        
        text_lower = text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        if len(words) < 5:
            return "english"
        
        # Count language indicators
        spanish_count = sum(1 for word in words if word in self.spanish_indicators)
        english_count = sum(1 for word in words if word in self.english_indicators)
        
        # Calculate percentages
        total_words = len(words)
        spanish_percentage = (spanish_count / total_words) * 100
        english_percentage = (english_count / total_words) * 100
        
        # Decision logic with threshold
        if spanish_percentage > english_percentage and spanish_percentage > 5:
            return "spanish"
        elif english_percentage > 5:
            return "english"
        else:
            # Fallback: check for specific patterns
            spanish_patterns = ['ción', 'sión', 'ñ', 'á', 'é', 'í', 'ó', 'ú']
            spanish_pattern_count = sum(1 for pattern in spanish_patterns if pattern in text_lower)
            
            return "spanish" if spanish_pattern_count > 2 else "english"
    
    def is_stop_word(self, word: str, language: str) -> bool:
        """
        Check if a word is a stop word in the given language.
        
        Args:
            word: Word to check
            language: 'spanish' or 'english'
            
        Returns:
            True if word is a stop word
        """
        word_lower = word.lower()
        
        if language == "spanish":
            return word_lower in self.spanish_stop_words
        else:
            return word_lower in self.english_stop_words
    
    def filter_meaningful_words(self, words: List[str], language: str) -> List[str]:
        """
        Filter out stop words and return meaningful words.
        
        Args:
            words: List of words to filter
            language: 'spanish' or 'english'
            
        Returns:
            List of meaningful words
        """
        meaningful_words = []
        
        for word in words:
            # Basic validation
            if len(word) < 3:
                continue
            if word.isdigit():
                continue
            if not re.match(r'^[a-záéíóúñA-ZÁÉÍÓÚÑ]+$', word):
                continue
            
            # Stop word check
            if not self.is_stop_word(word, language):
                meaningful_words.append(word)
        
        return meaningful_words
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[Tuple[str, int]]:
        """
        Extract meaningful keywords from text with frequency scoring.
        
        Args:
            text: Text to analyze
            max_keywords: Maximum number of keywords to return
            
        Returns:
            List of (keyword, frequency) tuples sorted by relevance
        """
        language = self.detect_language(text)
        
        # Extract words
        if language == "spanish":
            words = re.findall(r'\b[a-záéíóúñ]{3,}\b', text.lower())
        else:
            words = re.findall(r'\b[a-z]{3,}\b', text.lower())
        
        # Filter meaningful words
        meaningful_words = self.filter_meaningful_words(words, language)
        
        # Count frequencies
        word_counts = Counter(meaningful_words)
        
        # Score words (frequency + length bonus)
        scored_words = []
        for word, count in word_counts.items():
            score = count
            # Length bonus for more specific words
            if len(word) > 6:
                score += 3
            elif len(word) > 4:
                score += 1
            
            scored_words.append((word, score))
        
        # Sort by score and return top keywords
        scored_words.sort(key=lambda x: x[1], reverse=True)
        return scored_words[:max_keywords]
