import random
import re
from typing import List, Dict, Tuple, Optional, Set
import asyncio
import subprocess
import tempfile
import os
from collections import Counter
import math

# Optional dependencies with graceful fallbacks
try:
    from keybert import KeyBERT
    KEYBERT_AVAILABLE = True
except Exception:
    KeyBERT = None
    KEYBERT_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    ST_AVAILABLE = True
except Exception:
    SentenceTransformer = None
    ST_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except Exception:
    spacy = None
    SPACY_AVAILABLE = False

try:
    from langdetect import detect as _ld_detect
    LANGDETECT_AVAILABLE = True
except Exception:
    _ld_detect = None
    LANGDETECT_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except Exception:
    np = None
    NUMPY_AVAILABLE = False

# Simplified imports for testing - will use mock implementations
try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from keybert import KeyBERT
    KEYBERT_AVAILABLE = True
except ImportError:
    KEYBERT_AVAILABLE = False


class LanguageProcessor:
    """Enhanced language detection and processing"""

    def detect_language(self, text: str) -> str:
        """Detect language using langdetect if available, else heuristic"""
        txt = (text or "").strip()
        if not txt:
            return "english"

        if LANGDETECT_AVAILABLE:
            try:
                code = _ld_detect(txt)
                if code.startswith("es"):
                    return "spanish"
                if code.startswith("en"):
                    return "english"
                # Fallback to character-based detection
                return "spanish" if any(ch in txt for ch in "áéíóúñ¿¡") else "english"
            except Exception:
                pass

        # Heuristic fallback
        return "spanish" if any(ch in txt for ch in "áéíóúñ¿¡") else "english"


class WhisperTranscriber:
    def __init__(self, model_size: str = "base"):
        """Initialize Whisper transcriber with specified model size"""
        self.model_size = model_size
        self.model = None
        if WHISPER_AVAILABLE:
            self._load_model()

    def _load_model(self):
        """Load Whisper model"""
        if not WHISPER_AVAILABLE:
            print("Whisper not available - using mock transcription")
            return

        try:
            print(f"Loading Whisper model: {self.model_size}")
            self.model = whisper.load_model(self.model_size)
            print("Whisper model loaded successfully")
        except Exception as e:
            print(f"Failed to load Whisper model: {e}")
            self.model = None

    async def transcribe(self, video_path: str) -> Tuple[str, str]:
        """Transcribe video and detect language"""
        if not WHISPER_AVAILABLE or not self.model:
            # Mock transcription for testing
            print(f"Mock transcribing video: {video_path}")
            return "This is a mock transcript for testing purposes. The video contains sample content that would normally be transcribed by Whisper.", "en"

        try:
            # Run transcription in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._transcribe_sync,
                video_path
            )

            transcript = result["text"].strip()
            language = result["language"]

            return transcript, language

        except Exception as e:
            print(f"Transcription error: {e}")
            # Return mock data on error
            return "Error occurred during transcription. This is a fallback transcript.", "en"

    def _transcribe_sync(self, video_path: str) -> dict:
        """Synchronous transcription method"""
        return self.model.transcribe(
            video_path,
            language=None,  # Auto-detect language
            task="transcribe",
            verbose=False
        )

class TagGenerator:
    """High-quality, semantically meaningful tag generator with multi-word phrase support"""

    def __init__(self):
        """Initialize tag generator with advanced NLP capabilities"""
        self.language_processor = LanguageProcessor()
        self.st_model = None
        self.keybert_model = None
        self.nlp = None
        self._prefer_multilingual = True

        # Default color palette
        self.colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ]

        self._load_models()

    def _load_models(self):
        """Load NLP models with graceful fallbacks"""
        # Load spaCy model
        if SPACY_AVAILABLE:
            try:
                if self._prefer_multilingual:
                    # Try Spanish models first
                    for model_name in ["es_core_news_md", "es_core_news_sm", "es_core_news_lg"]:
                        try:
                            self.nlp = spacy.load(model_name)
                            print(f"Loaded spaCy model: {model_name}")
                            break
                        except OSError:
                            continue

                # Fallback to English models
                if self.nlp is None:
                    for model_name in ["en_core_web_md", "en_core_web_sm", "en_core_web_lg"]:
                        try:
                            self.nlp = spacy.load(model_name)
                            print(f"Loaded spaCy model: {model_name}")
                            break
                        except OSError:
                            continue
            except Exception as e:
                print(f"Failed to load spaCy models: {e}")

        # Load SentenceTransformer model
        if ST_AVAILABLE:
            try:
                if self._prefer_multilingual:
                    model_name = "paraphrase-multilingual-MiniLM-L12-v2"
                else:
                    model_name = "all-MiniLM-L6-v2"

                self.st_model = SentenceTransformer(model_name)
                print(f"Loaded SentenceTransformer model: {model_name}")
            except Exception as e:
                print(f"Failed to load SentenceTransformer: {e}")

        # Load KeyBERT model
        if KEYBERT_AVAILABLE and self.st_model:
            try:
                self.keybert_model = KeyBERT(model=self.st_model)
                print("Loaded KeyBERT with SentenceTransformer backend")
            except Exception as e:
                print(f"Failed to load KeyBERT: {e}")
        elif KEYBERT_AVAILABLE:
            try:
                self.keybert_model = KeyBERT()
                print("Loaded KeyBERT with default backend")
            except Exception as e:
                print(f"Failed to load KeyBERT: {e}")

    def _get_stopwords(self, language: str) -> Set[str]:
        """Get stopwords for the specified language"""
        if language == "spanish":
            return {
                'el', 'la', 'los', 'las', 'un', 'una', 'de', 'del', 'y', 'a', 'en', 'que', 'es', 'se', 'no',
                'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'como', 'las', 'pero', 'sus',
                'le', 'ya', 'o', 'porque', 'cuando', 'muy', 'sin', 'sobre', 'también', 'me', 'hasta', 'hay',
                'donde', 'quien', 'desde', 'todo', 'nos', 'durante', 'todos', 'uno', 'les', 'ni', 'contra',
                'otros', 'ese', 'eso', 'ante', 'ellos', 'e', 'esto', 'mí', 'antes', 'algunos', 'qué', 'unos',
                'yo', 'otro', 'otras', 'otra', 'él', 'tanto', 'esa', 'estos', 'mucho', 'quienes', 'nada',
                'muchos', 'cual', 'poco', 'ella', 'estar', 'estas', 'algunas', 'algo', 'nosotros', 'mi',
                'mis', 'tú', 'te', 'ti', 'tu', 'tus', 'ellas', 'nosotras', 'vosotros', 'vosotras',
                'os', 'mío', 'mía', 'míos', 'mías', 'tuyo', 'tuya', 'tuyos', 'tuyas', 'suyo', 'suya',
                'suyos', 'suyas', 'nuestro', 'nuestra', 'nuestros', 'nuestras', 'vuestro', 'vuestra',
                'vuestros', 'vuestras', 'esos', 'esas', 'estoy', 'estás', 'está', 'estamos', 'estáis',
                'están', 'esté', 'estés', 'estemos', 'estéis', 'estén', 'estaré', 'estarás', 'estará',
                'estaremos', 'estaréis', 'estarán', 'estaría', 'estarías', 'estaríamos', 'estaríais',
                'estarían', 'estaba', 'estabas', 'estábamos', 'estabais', 'estaban', 'estuve', 'estuviste',
                'estuvo', 'estuvimos', 'estuvisteis', 'estuvieron'
            }
        else:  # English
            return {
                'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours',
                'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers', 'herself',
                'it', 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which',
                'who', 'whom', 'this', 'that', 'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be',
                'been', 'being', 'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'a', 'an',
                'the', 'and', 'but', 'if', 'or', 'because', 'as', 'until', 'while', 'of', 'at', 'by',
                'for', 'with', 'through', 'during', 'before', 'after', 'above', 'below', 'up', 'down',
                'in', 'out', 'on', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here',
                'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more',
                'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so',
                'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now'
            }

    def _is_boilerplate(self, phrase: str) -> bool:
        """Check if phrase is boilerplate content to filter out"""
        phrase_lower = phrase.lower()
        boilerplate_terms = {
            'video', 'canal', 'app', 'subscribe', 'shorts', 'like', 'comment', 'share',
            'follow', 'notification', 'bell', 'content', 'youtube', 'tiktok', 'instagram',
            'facebook', 'twitter', 'social media', 'platform', 'algorithm', 'viral',
            'trending', 'hashtag', 'influencer', 'creator', 'channel', 'subscriber',
            'view', 'watch', 'click', 'link', 'description', 'bio', 'profile'
        }
        return any(term in phrase_lower for term in boilerplate_terms)

    def _stem_key(self, phrase: str) -> str:
        """Create a stem key for deduplication"""
        tokens = re.findall(r"[a-záéíóúüñ0-9]+", phrase.lower())
        return " ".join(sorted(set(tokens)))

    def _format_phrase(self, phrase: str) -> str:
        """Format phrase with proper capitalization"""
        special_cases = {
            "ai": "AI", "ml": "ML", "gpt": "GPT", "chatgpt": "ChatGPT",
            "llm": "LLM", "api": "API", "ui": "UI", "ux": "UX"
        }

        tokens = phrase.split()
        formatted_tokens = []

        for token in tokens:
            token_lower = token.lower()
            if token_lower in special_cases:
                formatted_tokens.append(special_cases[token_lower])
            else:
                formatted_tokens.append(token.capitalize())

        return " ".join(formatted_tokens)

    async def generate_tags(self, text: str, title: str = "", max_tags: int = 10) -> List[Dict[str, str]]:
        """Generate high-quality, semantically meaningful tags"""
        if not text.strip():
            return []

        try:
            # Run tag generation in executor to avoid blocking
            loop = asyncio.get_event_loop()
            tags = await loop.run_in_executor(
                None,
                self._generate_tags_sync,
                text,
                title,
                max_tags
            )
            return tags
        except Exception as e:
            print(f"Tag generation error: {e}")
            return self._fallback_tags(text, max_tags)

    def _generate_tags_sync(self, text: str, title: str = "", max_tags: int = 10) -> List[Dict[str, str]]:
        """Synchronous tag generation with semantic analysis"""
        try:
            # 1. Detect language
            language = self.language_processor.detect_language(text)
            print(f"Detected language: {language}")

            # 2. Extract candidate phrases
            candidates = self._extract_candidate_phrases(text, title, language)
            if not candidates:
                print("No candidates found, using fallback")
                return self._fallback_tags(text, max_tags)

            # 3. Semantic ranking if possible
            if self.st_model:
                ranked_candidates = self._rank_candidates_semantic(text, title, candidates, max_tags)
            else:
                # Fallback to frequency-based ranking
                ranked_candidates = self._rank_candidates_frequency(candidates, text, title, max_tags)

            # 4. Format and create final tags
            final_tags = []
            seen_stems = set()

            for candidate in ranked_candidates:
                if len(final_tags) >= max_tags:
                    break

                # Deduplication using stem key
                stem_key = self._stem_key(candidate)
                if stem_key in seen_stems:
                    continue
                seen_stems.add(stem_key)

                # Format and create tag
                formatted_name = self._format_phrase(candidate)
                category = self._detect_content_category_improved(text)

                tag = {
                    'name': formatted_name,
                    'color': random.choice(self.colors),
                    'description': self._generate_smart_description(formatted_name, category, language)
                }
                final_tags.append(tag)

            # 5. Apply Spanish corrections if needed
            if language == "spanish":
                final_tags = self._apply_spanish_corrections(final_tags)

            print(f"Generated {len(final_tags)} high-quality tags: {[tag['name'] for tag in final_tags]}")
            return final_tags

        except Exception as e:
            print(f"Error in semantic tag generation: {e}")
            return self._fallback_tags(text, max_tags)

    def _extract_candidate_phrases(self, text: str, title: str, language: str) -> List[str]:
        """Extract candidate phrases using spaCy if available, else regex fallback"""
        candidates = set()

        # Process title and text separately
        full_text = f"{title} {text}" if title else text

        if self.nlp:
            # Use spaCy for advanced phrase extraction
            candidates.update(self._extract_spacy_phrases(full_text, language))
        else:
            # Fallback to regex-based n-gram extraction
            candidates.update(self._extract_regex_phrases(full_text, language))

        # Clean and filter candidates
        cleaned_candidates = []
        for candidate in candidates:
            cleaned = self._clean_tag_name(candidate)
            if (cleaned and
                len(cleaned) >= 3 and
                not self._is_boilerplate(cleaned) and
                self._is_valid_keyword(cleaned, language)):
                cleaned_candidates.append(cleaned)

        return list(set(cleaned_candidates))

    def _extract_spacy_phrases(self, text: str, language: str) -> Set[str]:
        """Extract phrases using spaCy noun chunks and POS patterns"""
        candidates = set()

        try:
            doc = self.nlp(text)

            # Extract noun chunks
            for chunk in doc.noun_chunks:
                if 1 <= len(chunk) <= 4:  # Limit phrase length
                    candidates.add(chunk.text.strip())

            # Extract POS patterns: (ADJ|PROPN|NOUN)+
            current_phrase = []
            for token in doc:
                if token.pos_ in ['ADJ', 'PROPN', 'NOUN'] and not token.is_stop:
                    current_phrase.append(token.lemma_)
                else:
                    if 1 <= len(current_phrase) <= 4:
                        candidates.add(" ".join(current_phrase))
                    current_phrase = []

            # Don't forget the last phrase
            if 1 <= len(current_phrase) <= 4:
                candidates.add(" ".join(current_phrase))

        except Exception as e:
            print(f"Error in spaCy phrase extraction: {e}")

        return candidates

    def _extract_regex_phrases(self, text: str, language: str) -> Set[str]:
        """Fallback regex-based phrase extraction"""
        candidates = set()
        stopwords = self._get_stopwords(language)

        # Extract alphabetic tokens
        tokens = re.findall(r'[A-Za-zÁÉÍÓÚÜÑáéíóúüñ]+', text)
        tokens = [t.lower() for t in tokens if t.lower() not in stopwords and len(t) >= 3]

        # Generate n-grams (3, 2, 1)
        for n in [3, 2, 1]:
            for i in range(len(tokens) - n + 1):
                ngram = " ".join(tokens[i:i+n])
                candidates.add(ngram)

        return candidates



    def _rank_candidates_semantic(self, text: str, title: str, candidates: List[str], max_tags: int) -> List[str]:
        """Rank candidates using semantic similarity with MMR"""
        try:
            # Create weighted document
            sentences = text.split('.')[:3]  # First 3 sentences
            front_text = '. '.join(sentences)
            weighted_doc = f"{title} {title} {front_text} {text}"

            # Encode document and candidates
            doc_embedding = self._encode([weighted_doc])[0]
            candidate_embeddings = self._encode(candidates)

            # Apply MMR for diversity
            selected_indices = self._mmr(doc_embedding, candidate_embeddings, lamb=0.7, k=min(30, len(candidates)))
            selected_candidates = [candidates[i] for i in selected_indices]

            # Apply position and title boosts
            boosted_candidates = []
            for candidate in selected_candidates:
                boost = 0.0
                if title and candidate.lower() in title.lower():
                    boost += 0.2
                if candidate.lower() in front_text.lower():
                    boost += 0.1
                boosted_candidates.append((candidate, boost))

            # Sort by boost (stable sort preserves MMR order for ties)
            boosted_candidates.sort(key=lambda x: x[1], reverse=True)

            return [candidate for candidate, _ in boosted_candidates[:max_tags * 2]]

        except Exception as e:
            print(f"Error in semantic ranking: {e}")
            return candidates[:max_tags * 2]

    def _rank_candidates_frequency(self, candidates: List[str], text: str, title: str, max_tags: int) -> List[str]:
        """Fallback frequency-based ranking"""
        candidate_scores = {}
        text_lower = text.lower()
        title_lower = title.lower() if title else ""

        for candidate in candidates:
            candidate_lower = candidate.lower()
            score = text_lower.count(candidate_lower)

            # Boost for title appearance
            if title and candidate_lower in title_lower:
                score += 2

            # Boost for multi-word phrases
            if len(candidate.split()) > 1:
                score += 1

            candidate_scores[candidate] = score

        # Sort by score and length (prefer longer phrases)
        sorted_candidates = sorted(
            candidate_scores.items(),
            key=lambda x: (x[1], len(x[0].split())),
            reverse=True
        )

        return [candidate for candidate, _ in sorted_candidates[:max_tags * 2]]

    def _encode(self, texts: List[str]) -> List[List[float]]:
        """Encode texts using SentenceTransformer or fallback"""
        if self.st_model:
            try:
                embeddings = self.st_model.encode(texts, normalize_embeddings=True)
                return embeddings.tolist()
            except Exception as e:
                print(f"Error in encoding: {e}")

        # Fallback: return dummy embeddings
        return [[1.0, 1.0, 1.0] for _ in texts]

    def _mmr(self, doc_vec: List[float], cand_vecs: List[List[float]], lamb: float = 0.7, k: int = 10) -> List[int]:
        """Maximal Marginal Relevance for diverse selection"""
        if not NUMPY_AVAILABLE:
            # Simple fallback: return first k indices
            return list(range(min(k, len(cand_vecs))))

        try:
            doc_vec = np.array(doc_vec)
            cand_vecs = np.array(cand_vecs)

            selected = []
            remaining = list(range(len(cand_vecs)))

            while len(selected) < k and remaining:
                if not selected:
                    # First selection: highest similarity to document
                    similarities = np.dot(cand_vecs[remaining], doc_vec)
                    best_idx = remaining[np.argmax(similarities)]
                else:
                    # MMR selection
                    mmr_scores = []
                    selected_vecs = cand_vecs[selected]

                    for idx in remaining:
                        cand_vec = cand_vecs[idx]

                        # Relevance to document
                        relevance = np.dot(cand_vec, doc_vec)

                        # Maximum similarity to already selected
                        similarities = np.dot(selected_vecs, cand_vec)
                        max_sim = np.max(similarities) if len(similarities) > 0 else 0

                        # MMR score
                        mmr_score = lamb * relevance - (1 - lamb) * max_sim
                        mmr_scores.append(mmr_score)

                    best_idx = remaining[np.argmax(mmr_scores)]

                selected.append(best_idx)
                remaining.remove(best_idx)

            return selected

        except Exception as e:
            print(f"Error in MMR: {e}")
            return list(range(min(k, len(cand_vecs))))

    def _generate_tags_with_ollama(self, text: str, max_tags: int = 5, title: str = "") -> List[Dict[str, str]]:
        """Generate tags using Ollama local LLM with improved language-specific analysis"""
        try:
            import requests
            import json

            # Detect language first
            language = self.language_processor.detect_language(text)

            # Create language-specific prompt
            if language == "spanish":
                prompt = self._create_spanish_prompt(text, max_tags)
            else:
                prompt = self._create_english_prompt(text, max_tags)

            print(f"Detected language: {language}")
            print(f"Using language-specific prompt for tag generation")

            print(f"Sending prompt to Ollama for text: {text[:100]}...")

            # Use centralized configuration for Ollama endpoints
            from config import config
            ollama_endpoints = config.get_ollama_endpoints()

            response = None
            for endpoint in ollama_endpoints:
                try:
                    print(f"Trying Ollama endpoint: {endpoint}")
                    response = requests.post(
                        endpoint,
                        json={
                            'model': 'llama3.2:3b',
                            'prompt': prompt,
                            'stream': False,
                            'options': {
                                'temperature': 0.2,  # Low temperature for consistent results
                                'top_p': 0.8,
                                'num_predict': 200,  # Limit response length
                                'stop': ['\n\n', 'Human:', 'Assistant:']
                            }
                        },
                        timeout=30  # Reduced timeout for model processing
                    )
                    if response.status_code == 200:
                        print(f"Successfully connected to Ollama at {endpoint}")
                        break
                except requests.exceptions.RequestException as e:
                    print(f"Failed to connect to {endpoint}: {e}")
                    continue

            if response and response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()
                print(f"Ollama raw response: {generated_text}")

                # Parse the JSON response with multiple strategies
                parsed_data = self._parse_ollama_response(generated_text)

                if parsed_data:
                    category = parsed_data.get('category', 'General')
                    keywords = parsed_data.get('keywords', [])

                    print(f"Ollama extracted category: {category}")
                    print(f"Ollama extracted keywords: {keywords}")

                    # Validate and convert to tag format
                    tags = []
                    for keyword in keywords[:max_tags]:
                        if keyword and len(keyword.strip()) > 1:
                            clean_keyword = self._clean_tag_name(keyword.strip())
                            if clean_keyword and self._is_valid_keyword(clean_keyword, language):
                                tags.append({
                                    "name": clean_keyword,
                                    "color": random.choice(self.colors),
                                    "description": self._generate_smart_description(clean_keyword, category)
                                })

                    if len(tags) >= 3:  # Only return if we got at least 3 good tags
                        print(f"Ollama generated {len(tags)} high-quality tags")
                        return tags[:max_tags]
                    else:
                        print(f"Ollama generated only {len(tags)} valid tags, falling back to rule-based")
                else:
                    print("Failed to parse Ollama response, falling back to rule-based")
            elif response:
                print(f"Ollama API error: {response.status_code}")
            else:
                print("No successful Ollama connection, falling back to rule-based")

        except Exception as e:
            print(f"Ollama tag generation failed: {e}")

        return []  # Return empty list if Ollama fails

    def _parse_ollama_response(self, response_text: str) -> dict:
        """Parse Ollama response with multiple strategies"""
        import json

        # Strategy 1: Try to find JSON object directly
        try:
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
        except json.JSONDecodeError:
            pass

        # Strategy 2: Try to extract from code blocks
        try:
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
        except json.JSONDecodeError:
            pass

        # Strategy 3: Try to extract category and keywords separately
        try:
            category_match = re.search(r'"category":\s*"([^"]+)"', response_text)
            keywords_match = re.search(r'"keywords":\s*\[(.*?)\]', response_text, re.DOTALL)

            if category_match and keywords_match:
                category = category_match.group(1)
                keywords_str = keywords_match.group(1)
                # Extract keywords from the array string
                keywords = re.findall(r'"([^"]+)"', keywords_str)
                return {"category": category, "keywords": keywords}
        except Exception:
            pass

        print(f"Failed to parse Ollama response: {response_text}")
        return None

    def _is_valid_keyword(self, keyword: str, language: str = "english") -> bool:
        """Validate that a keyword is meaningful and not a stop word using language processor"""
        # Basic validation
        if len(keyword) < 3:
            return False

        if keyword.isdigit():
            return False

        # Check if it's repetitive (like "aaa" or "111")
        if len(set(keyword.lower())) < 2:
            return False

        # Check if it contains only valid characters for the language
        if language == "spanish":
            if not re.match(r'^[a-záéíóúñA-ZÁÉÍÓÚÑ]+$', keyword):
                return False
        else:
            if not re.match(r'^[a-zA-Z]+$', keyword):
                return False

        # Use language processor to check if it's a stop word
        if self.language_processor.is_stop_word(keyword, language):
            return False

        # Additional quality checks
        keyword_lower = keyword.lower()

        # Reject very common generic words that might not be in stop words
        generic_words = {
            'english': {'thing', 'things', 'stuff', 'something', 'anything', 'nothing',
                       'everything', 'someone', 'anyone', 'everyone', 'nobody'},
            'spanish': {'cosa', 'cosas', 'algo', 'nada', 'todo', 'alguien', 'nadie'}
        }

        if keyword_lower in generic_words.get(language, set()):
            return False

        return True

    def _apply_spanish_corrections(self, content_tags: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Apply Spanish grammar corrections to content-based tags"""
        corrected_tags = []

        for tag_data in content_tags:
            tag_name = tag_data.get('name', '')
            corrected_name = self.spanish_validator.correct_and_validate_tag(tag_name)

            if corrected_name:
                corrected_tags.append({
                    'name': corrected_name,
                    'category': tag_data.get('category', 'general'),
                    'frequency': tag_data.get('frequency', 1)
                })

        return corrected_tags

    def _apply_spanish_corrections_to_standard_tags(self, tags: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Apply Spanish grammar corrections to standard tag format"""
        corrected_tags = []

        for tag in tags:
            tag_name = tag.get('name', '')
            corrected_name = self.spanish_validator.correct_and_validate_tag(tag_name)

            if corrected_name:
                corrected_tags.append({
                    'name': corrected_name,
                    'color': tag.get('color', self._get_random_color()),
                    'description': self._generate_corrected_description(corrected_name, tag_name)
                })

        return corrected_tags

    def _format_tags(self, content_tags: List[Dict[str, str]], max_tags: int) -> List[Dict[str, str]]:
        """Format content-based tags to standard tag format"""
        formatted_tags = []

        for tag_data in content_tags[:max_tags]:
            tag_name = tag_data['name']
            category = tag_data.get('category', 'general')

            formatted_tags.append({
                'name': tag_name,
                'color': self._get_category_color(category),
                'description': self._generate_category_description(tag_name, category)
            })

        return formatted_tags

    def _get_category_color(self, category: str) -> str:
        """Get color based on content category"""
        category_colors = {
            'ingredient': '#10B981',  # Green for ingredients
            'technique': '#F59E0B',   # Orange for cooking techniques
            'dish': '#EF4444',        # Red for dish types
            'cuisine': '#8B5CF6',     # Purple for cuisine types
            'general': '#6B7280'      # Gray for general
        }
        return category_colors.get(category, random.choice(self.colors))

    def _generate_category_description(self, tag_name: str, category: str) -> str:
        """Generate description based on content category"""
        descriptions = {
            'ingredient': f"Ingrediente: {tag_name}",
            'technique': f"Técnica de cocina: {tag_name}",
            'dish': f"Tipo de plato: {tag_name}",
            'cuisine': f"Tipo de cocina: {tag_name}",
            'general': f"Contenido relacionado con {tag_name}"
        }
        return descriptions.get(category, f"Contenido sobre {tag_name}")

    def _generate_corrected_description(self, corrected_name: str, original_name: str) -> str:
        """Generate description noting any corrections made"""
        if corrected_name.lower() != original_name.lower():
            return f"Contenido sobre {corrected_name} (corregido de '{original_name}')"
        else:
            return f"Contenido relacionado con {corrected_name}"

    def _create_spanish_prompt(self, text: str, max_tags: int) -> str:
        """Create Spanish-specific prompt for content-based tag generation with grammar correction"""
        # Determine if this is cooking content for special handling
        is_cooking = any(word in text.lower() for word in ['cocina', 'receta', 'ingredientes', 'hornear', 'freír', 'queso', 'carne', 'pollo'])
        cooking_instruction = ""

        if is_cooking:
            max_tags = min(max_tags + 3, 8)  # Allow more tags for cooking content
            cooking_instruction = f"""
INSTRUCCIONES ESPECIALES PARA CONTENIDO DE COCINA:
- Extrae ingredientes específicos (ej: "Tomate", "Cebolla", "Pimienta", "Queso", "Maicena")
- Incluye técnicas de cocina (ej: "Freír", "Hornear", "Mezclar", "Rallar")
- Menciona tipos de platos (ej: "Merienda", "Entrada", "Bolitas", "Empanadas")
- Usa hasta {max_tags} etiquetas para cocina (más que el límite normal)
- CORRIGE errores ortográficos: "Pivienta" → "Pimienta", "Keso" → "Queso"
"""

        return f'''Eres un experto analizador de contenido en español con especialización en corrección gramatical. Analiza esta transcripción de video y extrae las {max_tags} palabras clave más importantes que representen el CONTENIDO REAL del video.

TRANSCRIPCIÓN: "{text}"

INSTRUCCIONES CRÍTICAS:
1. IGNORA completamente marcas de agua, nombres de apps y metadatos técnicos como "Snaptik", "TikTok", "Instagram", números de ID, extensiones de archivo
2. ENFÓCATE SOLO en el CONTENIDO REAL: ingredientes, técnicas, objetos, conceptos, temas
3. CORRIGE automáticamente errores ortográficos en español:
   - "Pivienta" → "Pimienta"
   - "Keso" → "Queso"
   - "Sevolla" → "Cebolla"
   - "Oregano" → "Orégano"
   - "Limon" → "Limón"
   - "Azucar" → "Azúcar"
4. USA ORTOGRAFÍA PERFECTA con acentos correctos
5. Extrae palabras que la gente BUSCARÍA para encontrar este contenido{cooking_instruction}

EJEMPLOS DE ANÁLISIS CORRECTO:

Video de cocina con queso y maicena:
❌ MALO: ["Snaptik", "Pivienta", "Video", "App", "Canal"]
✅ BUENO: ["Queso", "Maicena", "Pimienta", "Merienda", "Bolitas", "Freír", "Hornear"]

Video de remedios caseros:
❌ MALO: ["Los", "Para", "Como", "Simple", "Decidí"]
✅ BUENO: ["Hormigas", "Remedios", "Café", "Pimienta", "Natural", "Casero"]

Video de paella:
❌ MALO: ["Muy", "Está", "Porque", "También", "Video"]
✅ BUENO: ["Paella", "Arroz", "Azafrán", "Mariscos", "Valenciana", "Cocina", "Española"]

TAREA: Analiza el contenido REAL (ignorando metadatos técnicos) y extrae {max_tags} palabras clave con ortografía perfecta.

Devuelve SOLO un objeto JSON:
{{
  "category": "Comida/Cocina" o "Hogar/Estilo de vida" o "Tecnología" o "Educación" o "Entretenimiento",
  "keywords": ["palabra1", "palabra2", "palabra3", "palabra4", "palabra5"]
}}

Respuesta JSON:'''

    def _create_english_prompt(self, text: str, max_tags: int) -> str:
        """Create English-specific prompt for tag generation"""
        return f'''You are an expert content analyzer. Analyze this video transcript and extract the {max_tags} most important and searchable keywords that represent the core content.

TRANSCRIPT: "{text}"

INSTRUCTIONS FOR ENGLISH CONTENT:
1. Focus on the MAIN TOPIC and KEY CONCEPTS of the video
2. Extract CONCRETE NOUNS, SPECIFIC TOPICS, BRAND NAMES, and ACTIONABLE CONCEPTS
3. Choose keywords that someone would SEARCH FOR to find this video
4. Completely avoid filler words like: "the", "and", "that", "have", "for", "not", "with", "you", "this", "but", "his", "from", "they", "she", "her", "been", "than", "its", "who", "did", "yes", "get", "may", "him", "would", "could", "should", "will", "can", "about", "what", "when", "where", "why", "how", "very", "too", "so", "just", "now", "then", "here", "there", "all", "any", "both", "each", "few", "more", "most", "other", "some", "such", "only", "own", "same", "well", "back", "still", "way", "even", "new", "want", "because", "good", "people", "one", "show", "said", "time", "make", "like", "into", "has", "two", "go", "no", "could", "my", "first", "call", "who", "find", "long", "down", "day", "come", "made", "part", "video", "content", "channel", "watch", "viewer", "episode", "show", "series"

EXAMPLES OF GOOD vs BAD KEYWORDS:

For a home remedy video about ants:
✅ GOOD: ["Ants", "Remedies", "Coffee", "Pepper", "Home", "Natural", "Pest"]
❌ BAD: ["The", "And", "This", "Very", "Good", "Here", "Simple", "Video"]

For a remote jobs video:
✅ GOOD: ["Remote", "Jobs", "LinkedIn", "Websites", "Hiring", "Career", "Employment"]
❌ BAD: ["You", "And", "One", "Website", "Here", "That", "Will", "Can"]

For a cooking video:
✅ GOOD: ["Recipe", "Pasta", "Ingredients", "Cooking", "Kitchen", "Italian", "Food"]
❌ BAD: ["Video", "This", "Very", "Good", "Make", "Show", "Watch", "See"]

TASK: Analyze the transcript above and determine:
1. What is the main topic/category?
2. What are the {max_tags} most important searchable keywords?

Return ONLY a JSON object in this exact format:
{{
  "category": "Home/Lifestyle" or "Career/Business" or "Food/Cooking" or "Technology" or "Travel" or "Entertainment" or "Education",
  "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]
}}

JSON Response:'''

    def _clean_tag_name(self, text: str) -> str:
        """Clean and format tag name"""
        # Remove special characters and extra spaces
        cleaned = re.sub(r'[^\w\s-]', '', text)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # Capitalize first letter of each word
        cleaned = ' '.join(word.capitalize() for word in cleaned.split())
        
        return cleaned
    
    def _generate_tag_description(self, tag_name: str, context: str) -> str:
        """Generate a simple description for a tag based on context"""
        # Simple heuristic-based description generation
        context_lower = context.lower()
        tag_lower = tag_name.lower()
        
        if any(word in context_lower for word in ['music', 'song', 'dance', 'beat']):
            return f"Music/Audio related to {tag_name}"
        elif any(word in context_lower for word in ['food', 'cooking', 'recipe', 'eat']):
            return f"Food/Cooking content about {tag_name}"
        elif any(word in context_lower for word in ['travel', 'place', 'location', 'visit']):
            return f"Travel/Location content featuring {tag_name}"
        elif any(word in context_lower for word in ['funny', 'comedy', 'laugh', 'joke']):
            return f"Comedy/Entertainment content about {tag_name}"
        elif any(word in context_lower for word in ['tutorial', 'how to', 'learn', 'teach']):
            return f"Educational content about {tag_name}"
        else:
            return f"Content related to {tag_name}"
    
    def _extract_simple_tags(self, text: str, max_tags: int) -> List[Dict[str, str]]:
        """Improved rule-based tag extraction using language processor"""
        print("Using improved rule-based tag extraction as fallback")

        # Detect language using language processor
        language = self.language_processor.detect_language(text)
        print(f"Detected language: {language}")

        # Extract keywords using language processor
        keywords = self.language_processor.extract_keywords(text, max_keywords=max_tags * 2)

        # Convert to tag format
        tags = []
        for keyword, score in keywords[:max_tags]:
            # Validate keyword quality
            if self._is_valid_keyword(keyword, language):
                tag_name = self._clean_tag_name(keyword)
                if tag_name and len(tag_name) >= 3:
                    tags.append({
                        'name': tag_name,
                        'color': self._get_random_color(),
                        'description': self._generate_tag_description(tag_name, text)
                    })

        # If we don't have enough tags, try pattern-based extraction
        if len(tags) < max_tags:
            pattern_tags = self._extract_pattern_based_tags(text, language, max_tags - len(tags))
            tags.extend(pattern_tags)

        return tags[:max_tags]

    def _get_random_color(self) -> str:
        """Get a random color from the predefined palette"""
        return random.choice(self.colors)

    def _extract_pattern_based_tags(self, text: str, language: str, needed_tags: int) -> List[Dict[str, str]]:
        """Extract tags using pattern matching for specific domains"""
        tags = []
        text_lower = text.lower()

        # Define patterns for different languages
        if language == "spanish":
            patterns = {
                'cocina': ['cocina', 'receta', 'ingredientes', 'cocinar', 'comida', 'plato'],
                'hogar': ['casa', 'hogar', 'limpieza', 'decoración', 'muebles'],
                'salud': ['salud', 'medicina', 'ejercicio', 'bienestar', 'cuidado'],
                'tecnología': ['tecnología', 'computadora', 'móvil', 'aplicación', 'software'],
                'educación': ['aprender', 'enseñar', 'tutorial', 'curso', 'educación'],
                'viajes': ['viaje', 'turismo', 'destino', 'vacaciones', 'aventura']
            }
        else:
            patterns = {
                'cooking': ['cooking', 'recipe', 'ingredients', 'kitchen', 'food', 'dish'],
                'home': ['home', 'house', 'cleaning', 'decoration', 'furniture'],
                'health': ['health', 'medicine', 'exercise', 'wellness', 'care'],
                'technology': ['technology', 'computer', 'mobile', 'app', 'software'],
                'education': ['learn', 'teach', 'tutorial', 'course', 'education'],
                'travel': ['travel', 'tourism', 'destination', 'vacation', 'adventure']
            }

        # Find matching patterns
        for category, keywords in patterns.items():
            if any(keyword in text_lower for keyword in keywords):
                if not any(tag['name'].lower() == category for tag in tags):
                    tags.append({
                        'name': category.title(),
                        'color': self._get_random_color(),
                        'description': self._generate_tag_description(category, text)
                    })
                    if len(tags) >= needed_tags:
                        break

        return tags
        category = self._detect_content_category_improved(text)
        print(f"Detected language: {'Spanish' if is_spanish else 'English'}, Category: {category}")

        # Extract meaningful keywords using improved heuristics
        keywords = self._extract_meaningful_keywords_improved(text, is_spanish, category)

        # Convert to tag format with validation
        tags = []
        for keyword, score in keywords[:max_tags * 2]:  # Get more candidates
            tag_name = self._clean_tag_name(keyword)
            if tag_name and self._is_valid_keyword(tag_name) and len(tag_name) > 2:
                tags.append({
                    "name": tag_name,
                    "color": random.choice(self.colors),
                    "description": self._generate_smart_description(tag_name, category)
                })

                if len(tags) >= max_tags:
                    break

        print(f"Rule-based extraction generated {len(tags)} tags: {[tag['name'] for tag in tags]}")
        return tags

    def _is_spanish_text(self, text: str) -> bool:
        """Detect if text is primarily Spanish"""
        spanish_indicators = ['que', 'para', 'con', 'una', 'los', 'las', 'del', 'como', 'esto', 'esta', 'este']
        text_lower = text.lower()
        spanish_count = sum(1 for word in spanish_indicators if word in text_lower)
        return spanish_count >= 3

    def _detect_content_category_improved(self, text: str) -> str:
        """Improved category detection based on content analysis"""
        text_lower = text.lower()

        # Define category keywords with higher specificity
        category_patterns = {
            'Home/Lifestyle': [
                'remedios', 'casa', 'hogar', 'limpieza', 'hormigas', 'insectos', 'cucarachas',
                'home', 'house', 'cleaning', 'ants', 'insects', 'remedy', 'diy', 'tips'
            ],
            'Career/Business': [
                'trabajo', 'empleo', 'carrera', 'negocio', 'empresa', 'marketing', 'ventas',
                'job', 'work', 'career', 'business', 'employment', 'hiring', 'remote', 'linkedin',
                'resume', 'interview', 'salary', 'freelance'
            ],
            'Food/Cooking': [
                'receta', 'cocina', 'comida', 'ingredientes', 'cocinar', 'horno', 'café',
                'recipe', 'cooking', 'food', 'ingredients', 'kitchen', 'cook', 'bake', 'chef'
            ],
            'Technology': [
                'tecnología', 'software', 'app', 'programación', 'código', 'inteligencia artificial',
                'technology', 'software', 'programming', 'code', 'ai', 'artificial intelligence',
                'computer', 'digital', 'tech', 'algorithm'
            ],
            'Travel': [
                'viaje', 'turismo', 'vacaciones', 'avión', 'hotel', 'destino',
                'travel', 'trip', 'vacation', 'flight', 'hotel', 'destination', 'tourism'
            ],
            'Education': [
                'tutorial', 'aprender', 'enseñar', 'curso', 'lección', 'educación',
                'tutorial', 'learn', 'teach', 'course', 'lesson', 'education', 'guide', 'how to'
            ]
        }

        # Score each category
        category_scores = {}
        for category, keywords in category_patterns.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                category_scores[category] = score

        # Return the highest scoring category or General
        if category_scores:
            best_category = max(category_scores, key=category_scores.get)
            print(f"Category scores: {category_scores}, Selected: {best_category}")
            return best_category

        return 'General'

    def _extract_meaningful_keywords_improved(self, text: str, is_spanish: bool, category: str) -> List[tuple]:
        """Extract meaningful keywords with improved filtering and scoring"""

        # Get all words
        if is_spanish:
            words = re.findall(r'\b[a-záéíóúñ]{3,}\b', text.lower())
        else:
            words = re.findall(r'\b[a-z]{3,}\b', text.lower())

        # Score each unique word
        word_scores = {}
        for word in set(words):
            if self._is_valid_keyword(word):
                score = self._calculate_keyword_score_improved(word, text, category, is_spanish)
                if score > 0:
                    word_scores[word] = score

        # Sort by score and return top candidates
        return sorted(word_scores.items(), key=lambda x: x[1], reverse=True)

    def _calculate_keyword_score_improved(self, keyword: str, text: str, category: str, is_spanish: bool) -> int:
        """Calculate improved relevance score for a keyword"""
        text_lower = text.lower()
        score = 0

        # Base frequency score (but cap it to avoid over-weighting common words)
        frequency = min(text_lower.count(keyword.lower()), 3)
        score += frequency

        # Length bonus (longer words are often more specific)
        if len(keyword) > 5:
            score += 3
        elif len(keyword) > 4:
            score += 1

        # Capitalization bonus (proper nouns in original text)
        if any(word.istitle() for word in re.findall(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE)):
            score += 4

        # Category relevance boost
        category_keywords = self._get_category_keywords_improved(category, is_spanish)
        if keyword.lower() in category_keywords:
            score += 8

        # Specific content type bonuses
        if is_spanish:
            # Spanish-specific meaningful words
            meaningful_spanish = {
                'remedios', 'hormigas', 'insectos', 'café', 'pimienta', 'laurel', 'clavos',
                'receta', 'ingredientes', 'cocina', 'casa', 'hogar', 'trabajo', 'empleo'
            }
            if keyword in meaningful_spanish:
                score += 6
        else:
            # English-specific meaningful words
            meaningful_english = {
                'remote', 'jobs', 'linkedin', 'websites', 'hiring', 'career', 'business',
                'recipe', 'cooking', 'ingredients', 'tutorial', 'guide', 'tips'
            }
            if keyword in meaningful_english:
                score += 6

        return score

    def _get_category_keywords_improved(self, category: str, is_spanish: bool) -> set:
        """Get improved category-specific keywords"""
        if is_spanish:
            keywords_map = {
                'Home/Lifestyle': {'remedios', 'casa', 'hogar', 'limpieza', 'hormigas', 'insectos', 'cucarachas'},
                'Career/Business': {'trabajo', 'empleo', 'carrera', 'negocio', 'empresa', 'marketing'},
                'Food/Cooking': {'receta', 'cocina', 'comida', 'ingredientes', 'cocinar', 'café'},
                'Technology': {'tecnología', 'software', 'programación', 'código'},
                'Travel': {'viaje', 'turismo', 'vacaciones', 'avión', 'hotel'},
                'Education': {'tutorial', 'aprender', 'enseñar', 'curso', 'lección'}
            }
        else:
            keywords_map = {
                'Home/Lifestyle': {'home', 'house', 'cleaning', 'ants', 'insects', 'remedy', 'diy', 'tips'},
                'Career/Business': {'job', 'work', 'career', 'business', 'employment', 'hiring', 'remote', 'linkedin'},
                'Food/Cooking': {'recipe', 'cooking', 'food', 'ingredients', 'kitchen', 'cook', 'chef'},
                'Technology': {'technology', 'software', 'programming', 'code', 'ai', 'computer', 'tech'},
                'Travel': {'travel', 'trip', 'vacation', 'flight', 'hotel', 'destination'},
                'Education': {'tutorial', 'learn', 'teach', 'course', 'lesson', 'education', 'guide'}
            }

        return keywords_map.get(category, set())

    def _extract_meaningful_keywords(self, text: str, category: str, max_tags: int) -> List[tuple]:
        """Extract meaningful keywords using improved heuristics"""

        # Language detection
        is_spanish = any(word in text.lower() for word in ['que', 'para', 'con', 'una', 'los', 'las', 'del', 'como'])

        # Define meaningful word patterns based on content analysis
        if is_spanish:
            keywords = self._extract_spanish_keywords(text, category)
        else:
            keywords = self._extract_english_keywords(text, category)

        # Score and rank keywords
        scored_keywords = []
        for keyword in keywords:
            score = self._calculate_keyword_score(keyword, text, category)
            if score > 0:
                scored_keywords.append((keyword, score))

        # Sort by score and return top keywords
        return sorted(scored_keywords, key=lambda x: x[1], reverse=True)[:max_tags * 2]

    def _extract_spanish_keywords(self, text: str, category: str) -> List[str]:
        """Extract meaningful Spanish keywords"""
        text_lower = text.lower()

        # Common Spanish stop words to avoid
        stop_words = {
            'que', 'para', 'con', 'una', 'los', 'las', 'del', 'como', 'esto', 'esta', 'este',
            'son', 'pero', 'todo', 'más', 'muy', 'ahora', 'aquí', 'donde', 'cuando', 'porque',
            'desde', 'hasta', 'sobre', 'entre', 'sin', 'cada', 'otro', 'otra', 'todos', 'todas',
            'mismo', 'misma', 'bien', 'solo', 'sólo', 'también', 'así', 'entonces', 'después',
            'antes', 'mientras', 'durante', 'contra', 'hacia', 'según', 'bajo', 'sobre',
            'tenés', 'tengo', 'tiene', 'hacer', 'hice', 'hago', 'vamos', 'dale', 'gusta',
            'gustar', 'estos', 'estas', 'esos', 'esas', 'aquel', 'aquella', 'mandamos',
            'verdad', 'botón', 'apretar', 'compartir', 'amo', 'bah', 'nunca', 'siempre',
            'puede', 'pueden', 'puedes', 'podemos', 'dijo', 'dice', 'decir', 'hacer',
            'hecho', 'hecha', 'sido', 'estar', 'estoy', 'está', 'están', 'estás'
        }

        # Extract nouns and meaningful words
        words = re.findall(r'\b[a-záéíóúñ]{3,}\b', text_lower)

        # Filter meaningful words
        meaningful_words = []
        for word in words:
            if (word not in stop_words and
                len(word) > 2 and
                not word.isdigit() and
                word.count(word[0]) < len(word) * 0.7):  # Avoid repetitive words
                meaningful_words.append(word)

        return list(set(meaningful_words))

    def _extract_english_keywords(self, text: str, category: str) -> List[str]:
        """Extract meaningful English keywords"""
        text_lower = text.lower()

        # Common English stop words to avoid
        stop_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy',
            'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use', 'way', 'will',
            'this', 'that', 'with', 'have', 'from', 'they', 'know', 'want', 'been',
            'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just',
            'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them',
            'well', 'were', 'what', 'your', 'going', 'really', 'think', 'about',
            'would', 'could', 'should', 'there', 'where', 'right', 'first', 'after',
            'back', 'other', 'more', 'most', 'also', 'into', 'only', 'then', 'look',
            'people', 'things', 'something', 'anything', 'everything', 'nothing'
        }

        # Extract nouns and meaningful words
        words = re.findall(r'\b[a-z]{3,}\b', text_lower)

        # Filter meaningful words
        meaningful_words = []
        for word in words:
            if (word not in stop_words and
                len(word) > 2 and
                not word.isdigit() and
                word.count(word[0]) < len(word) * 0.7):  # Avoid repetitive words
                meaningful_words.append(word)

        return list(set(meaningful_words))

    def _calculate_keyword_score(self, keyword: str, text: str, category: str) -> int:
        """Calculate relevance score for a keyword"""
        text_lower = text.lower()
        score = 0

        # Base frequency score
        frequency = text_lower.count(keyword.lower())
        score += frequency * 2

        # Category relevance boost
        category_keywords = self._get_category_keywords(category)
        if keyword.lower() in category_keywords:
            score += 10

        # Length bonus (longer words are often more specific)
        if len(keyword) > 5:
            score += 2

        # Capitalization bonus (proper nouns)
        if any(word.istitle() for word in re.findall(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE)):
            score += 3

        # Penalize very common words
        very_common = {'video', 'aquí', 'esto', 'hacer', 'puede', 'tiene', 'para', 'como'}
        if keyword.lower() in very_common:
            score -= 5

        return max(0, score)

    def _detect_content_category(self, text: str) -> str:
        """Detect the main category of the content with improved accuracy"""
        text_lower = text.lower()

        # Business/Marketing keywords
        business_keywords = [
            'business', 'brand', 'branding', 'marketing', 'agency', 'design', 'logo',
            'strategy', 'strategist', 'company', 'startup', 'entrepreneur', 'corporate',
            'client', 'customer', 'sales', 'revenue', 'profit', 'investment', 'investor',
            'pitch', 'presentation', 'meeting', 'conference', 'networking', 'professional',
            'industry', 'market', 'competition', 'competitor', 'analysis', 'research',
            'plan', 'planning', 'development', 'growth', 'scale', 'scaling', 'launch',
            'product', 'service', 'solution', 'innovation', 'creative', 'campaign',
            'advertising', 'promotion', 'social media', 'content marketing', 'seo',
            'website', 'digital marketing', 'online presence', 'e-commerce', 'retail'
        ]

        # Technology/AI keywords
        tech_keywords = [
            'ai', 'artificial intelligence', 'chatgpt', 'chat gpt', 'gpt', 'openai',
            'machine learning', 'automation', 'algorithm', 'data', 'analytics',
            'software', 'app', 'application', 'platform', 'tool', 'technology', 'tech',
            'digital', 'online', 'internet', 'web', 'website', 'interface', 'ui', 'ux',
            'programming', 'coding', 'development', 'developer', 'engineer', 'computer',
            'system', 'database', 'cloud', 'server', 'api', 'integration', 'mobile',
            'smartphone', 'tablet', 'device', 'gadget', 'innovation', 'startup tech'
        ]

        # Design/Creative keywords
        design_keywords = [
            'design', 'designer', 'graphic', 'visual', 'creative', 'art', 'artist',
            'illustration', 'logo', 'branding', 'identity', 'color', 'colours', 'hex',
            'typography', 'font', 'layout', 'composition', 'aesthetic', 'style',
            'mood board', 'moodboard', 'inspiration', 'pinterest', 'behance', 'dribbble',
            'photoshop', 'illustrator', 'figma', 'sketch', 'canva', 'adobe', 'portfolio',
            'packaging', 'label', 'print', 'digital design', 'web design', 'ui design'
        ]

        # Food/Cooking keywords (English and Spanish)
        food_keywords = [
            'recipe', 'cook', 'cooking', 'food', 'eat', 'eating', 'kitchen', 'ingredient',
            'dish', 'meal', 'restaurant', 'chef', 'bake', 'baking', 'oven', 'stove',
            'receta', 'cocina', 'comida', 'comer', 'ingrediente', 'plato', 'horno',
            'chipá', 'calabaza', 'queso', 'huevo', 'aceite', 'leche', 'sal', 'masa',
            'desayunar', 'adictivos', 'procesarla', 'fecula', 'mandioca', 'doraditos'
        ]

        # Travel/Transportation keywords
        travel_keywords = [
            'airplane', 'airport', 'flight', 'travel', 'traveling', 'plane', 'seat',
            'luggage', 'board', 'boarding', 'overhead', 'bin', 'cart', 'wheel', 'install',
            'vacation', 'holiday', 'trip', 'journey', 'destination', 'hotel', 'resort',
            'avión', 'aeropuerto', 'viaje', 'viajar', 'vacaciones'
        ]

        # Entertainment/Comedy keywords
        entertainment_keywords = [
            'funny', 'comedy', 'laugh', 'laughing', 'joke', 'humor', 'entertainment',
            'show', 'performance', 'actor', 'actress', 'movie', 'film', 'tv', 'series',
            'gracioso', 'comedia', 'risa', 'entretenimiento', 'película'
        ]

        # Music/Dance keywords
        music_keywords = [
            'music', 'musical', 'song', 'singing', 'dance', 'dancing', 'beat', 'rhythm',
            'sing', 'musician', 'artist', 'band', 'concert', 'performance', 'instrument',
            'música', 'canción', 'baile', 'ritmo', 'cantar', 'artista'
        ]

        # Education/Tutorial keywords
        education_keywords = [
            'tutorial', 'how to', 'learn', 'learning', 'teach', 'teaching', 'education',
            'educational', 'course', 'lesson', 'guide', 'tip', 'tips', 'advice',
            'explain', 'explanation', 'demonstrate', 'demonstration', 'step by step',
            'tutorial', 'cómo', 'aprender', 'enseñar', 'educación', 'consejo'
        ]

        # Count category matches with weighted scoring
        categories = {}

        # Business/Marketing (high priority for business content)
        business_score = sum(3 if keyword in text_lower else 0 for keyword in business_keywords)
        categories['Business'] = business_score

        # Technology/AI (high priority for tech content)
        tech_score = sum(3 if keyword in text_lower else 0 for keyword in tech_keywords)
        categories['Technology'] = tech_score

        # Design/Creative (high priority for design content)
        design_score = sum(3 if keyword in text_lower else 0 for keyword in design_keywords)
        categories['Design'] = design_score

        # Other categories (normal scoring)
        categories['Food/Cooking'] = sum(1 for keyword in food_keywords if keyword in text_lower)
        categories['Travel'] = sum(1 for keyword in travel_keywords if keyword in text_lower)
        categories['Entertainment'] = sum(1 for keyword in entertainment_keywords if keyword in text_lower)
        categories['Music'] = sum(1 for keyword in music_keywords if keyword in text_lower)
        categories['Education'] = sum(1 for keyword in education_keywords if keyword in text_lower)

        # Debug logging
        print(f"Category scores: {categories}")

        # Return the category with the highest score, or 'General' if no clear match
        best_category = max(categories.items(), key=lambda x: x[1])
        detected_category = best_category[0] if best_category[1] > 0 else 'General'
        print(f"Detected category: {detected_category} (score: {best_category[1]})")

        return detected_category

    def _extract_content_words(self, text: str, category: str) -> List[tuple]:
        """Extract meaningful content words using advanced NLP techniques"""

        # Enhanced stop words (English and Spanish)
        stop_words = {
            # English common words
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy',
            'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use', 'way', 'will',
            'this', 'that', 'with', 'have', 'from', 'they', 'know', 'want', 'been',
            'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just',
            'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them',
            'well', 'were', 'what', 'your', 'going', 'really', 'think', 'about',
            'would', 'could', 'should', 'there', 'where', 'right', 'first', 'after',
            'back', 'other', 'more', 'most', 'also', 'into', 'only', 'then', 'look',
            'people', 'things', 'something', 'anything', 'everything', 'nothing',
            'want', 'dont', 'here', 'work', 'been', 'pretty', 'crazy', 'tool',
            'upload', 'ask', 'act', 'world', 'class', 'developing', 'compelling',
            'story', 'voice', 'tone', 'direction', 'generate', 'using', 'own',
            'inspiration', 'pop', 'onto', 'make', 'board', 'style', 'look', 'feel',
            'download', 'these', 'images', 'them', 'into', 'document', 'upload',
            'assets', 'products', 'labels', 'packaging', 'company', 'interface',
            'more', 'carbs', 'create', 'elevate', 'current', 'comment', 'guide',
            'send', 'prompts',

            # Spanish common words
            'como', 'que', 'con', 'por', 'para', 'una', 'los', 'las', 'del', 'esto',
            'esta', 'este', 'son', 'pero', 'todo', 'más', 'muy', 'ahora', 'aquí',
            'donde', 'cuando', 'porque', 'desde', 'hasta', 'sobre', 'entre', 'sin',
            'cada', 'otro', 'otra', 'todos', 'todas', 'mismo', 'misma', 'bien',
            'solo', 'sólo', 'también', 'así', 'entonces', 'después', 'antes',
            'mientras', 'durante', 'contra', 'hacia', 'según', 'bajo', 'sobre',
            'tenés', 'tengo', 'tiene', 'hacer', 'hice', 'hago', 'vamos', 'dale',
            'gusta', 'gustar', 'estos', 'estas', 'esos', 'esas', 'aquel', 'aquella',
            'mandamos', 'verdad', 'botón', 'apretar', 'compartir', 'amo', 'bah',

            # Generic video/social media words
            'video', 'tiktok', 'content', 'watch', 'subscribe', 'follow', 'comment',
            'like', 'share', 'click', 'button'
        }

        # Extract meaningful keywords using multiple strategies
        keywords = self._extract_keywords_multi_strategy(text, category, stop_words)

        return keywords

    def _extract_keywords_multi_strategy(self, text: str, category: str, stop_words: set) -> List[tuple]:
        """Extract keywords using multiple strategies for better accuracy"""

        # Strategy 1: Extract proper nouns and capitalized words
        proper_nouns = self._extract_proper_nouns(text, stop_words)

        # Strategy 2: Extract category-specific keywords
        category_keywords = self._extract_category_specific_keywords(text, category, stop_words)

        # Strategy 3: Extract compound terms and phrases
        compound_terms = self._extract_compound_terms(text, stop_words)

        # Strategy 4: Extract technical terms and acronyms
        technical_terms = self._extract_technical_terms(text, stop_words)

        # Combine all strategies with weighted scoring
        all_keywords = {}

        # Add proper nouns (high weight)
        for word, score in proper_nouns:
            all_keywords[word] = all_keywords.get(word, 0) + score * 3

        # Add category keywords (high weight)
        for word, score in category_keywords:
            all_keywords[word] = all_keywords.get(word, 0) + score * 4

        # Add compound terms (medium weight)
        for word, score in compound_terms:
            all_keywords[word] = all_keywords.get(word, 0) + score * 2

        # Add technical terms (high weight)
        for word, score in technical_terms:
            all_keywords[word] = all_keywords.get(word, 0) + score * 3

        # Sort by combined score
        return sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)

    def _extract_proper_nouns(self, text: str, stop_words: set) -> List[tuple]:
        """Extract proper nouns and capitalized words"""
        words = re.findall(r'\b[A-Z][a-zA-Z]{2,}\b', text)
        word_counts = {}

        for word in words:
            word_lower = word.lower()
            if (word_lower not in stop_words and
                len(word) > 2 and
                not word_lower in ['chat', 'gbt']):  # Exclude common false positives
                word_counts[word] = word_counts.get(word, 0) + 1

        return list(word_counts.items())

    def _extract_category_specific_keywords(self, text: str, category: str, stop_words: set) -> List[tuple]:
        """Extract keywords specific to the detected category"""
        category_keywords = self._get_category_keywords(category)
        text_lower = text.lower()

        found_keywords = {}
        for keyword in category_keywords:
            if keyword in text_lower and keyword not in stop_words:
                # Count occurrences
                count = text_lower.count(keyword)
                if count > 0:
                    found_keywords[keyword] = count

        return list(found_keywords.items())

    def _extract_compound_terms(self, text: str, stop_words: set) -> List[tuple]:
        """Extract meaningful compound terms and phrases"""
        # Look for compound terms like "brand identity", "chat gpt", etc.
        compound_patterns = [
            r'\b(chat\s+gpt|chatgpt)\b',
            r'\b(brand\s+identity)\b',
            r'\b(brand\s+strategy)\b',
            r'\b(brand\s+assets)\b',
            r'\b(mood\s+board)\b',
            r'\b(image\s+generator)\b',
            r'\b(business\s+plan)\b',
            r'\b(brand\s+story)\b',
            r'\b(brand\s+colours)\b',
            r'\b(artificial\s+intelligence)\b',
            r'\b(machine\s+learning)\b',
            r'\b(user\s+interface)\b',
            r'\b(graphic\s+design)\b',
        ]

        found_compounds = {}
        text_lower = text.lower()

        for pattern in compound_patterns:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                # Normalize the compound term
                normalized = re.sub(r'\s+', ' ', match.strip())
                if normalized:
                    found_compounds[normalized] = found_compounds.get(normalized, 0) + 1

        return list(found_compounds.items())

    def _extract_technical_terms(self, text: str, stop_words: set) -> List[tuple]:
        """Extract technical terms, acronyms, and specialized vocabulary"""
        # Technical terms that are important regardless of category
        technical_terms = {
            'ai', 'artificial intelligence', 'chatgpt', 'chat gpt', 'gpt',
            'logo', 'branding', 'design', 'ui', 'ux', 'interface',
            'strategy', 'marketing', 'business', 'brand', 'identity',
            'pinterest', 'photoshop', 'illustrator', 'figma', 'canva',
            'programming', 'coding', 'software', 'app', 'website',
            'algorithm', 'data', 'analytics', 'automation', 'digital',
            'startup', 'entrepreneur', 'innovation', 'technology',
            'recipe', 'cooking', 'ingredients', 'kitchen', 'food',
            'travel', 'vacation', 'flight', 'hotel', 'destination'
        }

        text_lower = text.lower()
        found_terms = {}

        for term in technical_terms:
            if term in text_lower:
                count = text_lower.count(term)
                if count > 0:
                    found_terms[term] = count

        return list(found_terms.items())

    def _get_category_keywords(self, category: str) -> set:
        """Get relevant keywords for each category with enhanced specificity"""
        keywords = {
            'Business': {
                'business', 'brand', 'branding', 'marketing', 'agency', 'strategy',
                'strategist', 'company', 'startup', 'entrepreneur', 'client', 'customer',
                'sales', 'revenue', 'investment', 'pitch', 'professional', 'industry',
                'market', 'competition', 'analysis', 'plan', 'development', 'growth',
                'product', 'service', 'solution', 'campaign', 'advertising', 'promotion',
                'brand identity', 'brand story', 'brand assets', 'business plan',
                'brand strategy', 'brand colours', 'brand guide'
            },
            'Technology': {
                'ai', 'artificial intelligence', 'chatgpt', 'chat gpt', 'gpt', 'openai',
                'automation', 'algorithm', 'software', 'app', 'platform', 'tool',
                'technology', 'digital', 'online', 'website', 'interface', 'programming',
                'development', 'computer', 'system', 'cloud', 'mobile', 'device',
                'innovation', 'machine learning', 'data', 'analytics', 'coding',
                'image generator', 'prompt', 'tech company'
            },
            'Design': {
                'design', 'designer', 'graphic', 'visual', 'creative', 'logo', 'identity',
                'color', 'colours', 'hex', 'typography', 'layout', 'aesthetic', 'style',
                'mood board', 'moodboard', 'inspiration', 'pinterest', 'portfolio',
                'packaging', 'label', 'photoshop', 'illustrator', 'figma', 'canva',
                'brand identity', 'graphic design', 'ui', 'ux', 'user interface',
                'visual identity', 'design assets', 'brand assets'
            },
            'Food/Cooking': {
                'chipá', 'calabaza', 'queso', 'huevo', 'aceite', 'leche', 'sal', 'masa',
                'horno', 'receta', 'cocina', 'ingrediente', 'desayunar', 'comida',
                'procesarla', 'fecula', 'mandioca', 'doraditos', 'adictivos',
                'recipe', 'cook', 'cooking', 'food', 'kitchen', 'ingredient', 'dish',
                'meal', 'eat', 'bake', 'baking', 'chef', 'restaurant', 'cuisine'
            },
            'Travel': {
                'airplane', 'airport', 'flight', 'travel', 'plane', 'seat', 'luggage',
                'board', 'overhead', 'bin', 'cart', 'wheel', 'install', 'vacation',
                'holiday', 'trip', 'journey', 'destination', 'hotel', 'resort',
                'tourism', 'sightseeing', 'adventure', 'backpacking'
            },
            'Entertainment': {
                'funny', 'comedy', 'laugh', 'joke', 'humor', 'entertainment', 'show',
                'performance', 'actor', 'movie', 'film', 'series', 'tv', 'cinema',
                'theater', 'stage', 'drama', 'comedy show'
            },
            'Music': {
                'music', 'song', 'dance', 'beat', 'rhythm', 'sing', 'musician',
                'artist', 'band', 'concert', 'instrument', 'guitar', 'piano',
                'drums', 'vocals', 'melody', 'harmony', 'composition'
            },
            'Education': {
                'tutorial', 'learn', 'teach', 'education', 'course', 'lesson',
                'guide', 'tip', 'advice', 'explain', 'demonstrate', 'step',
                'how to', 'instruction', 'training', 'workshop', 'seminar'
            }
        }
        return keywords.get(category, set())

    def _generate_category_description(self, tag_name: str, category: str) -> str:
        """Generate description based on detected category"""
        if category == 'Business':
            return f"Business: {tag_name}"
        elif category == 'Technology':
            return f"Tech: {tag_name}"
        elif category == 'Design':
            return f"Design: {tag_name}"
        elif category == 'Food/Cooking':
            return f"Cooking: {tag_name}"
        elif category == 'Travel':
            return f"Travel: {tag_name}"
        elif category == 'Entertainment':
            return f"Entertainment: {tag_name}"
        elif category == 'Music':
            return f"Music: {tag_name}"
        elif category == 'Education':
            return f"Education: {tag_name}"
        else:
            return f"Content: {tag_name}"

    def _extract_important_words(self, text: str) -> List[str]:
        """Extract potentially important words using simple patterns"""
        important_words = []

        # Extract capitalized words (potential proper nouns)
        capitalized = re.findall(r'\b[A-Z][a-z]{2,}\b', text)
        important_words.extend(capitalized)

        # Extract words that appear in quotes
        quoted = re.findall(r'"([^"]*)"', text)
        for quote in quoted:
            words = re.findall(r'\b[a-zA-Z]{3,}\b', quote)
            important_words.extend(words)

        # Extract hashtag-like words (words preceded by #)
        hashtags = re.findall(r'#(\w+)', text)
        important_words.extend(hashtags)

        # Extract words that appear with emphasis (ALL CAPS)
        emphasized = re.findall(r'\b[A-Z]{3,}\b', text)
        important_words.extend([word.lower() for word in emphasized])

        return list(set(important_words))

    def _generate_smart_description(self, tag_name: str, category: str, language: str = "english") -> str:
        """Generate smart descriptions based on category and language"""
        if language == "spanish":
            category_map = {
                'cooking': 'Cocina',
                'tech': 'Tecnología',
                'general': 'Contenido',
                'home': 'Hogar',
                'business': 'Negocios',
                'travel': 'Viajes',
                'entertainment': 'Entretenimiento',
                'education': 'Educación'
            }
            category_short = category_map.get(category.lower(), 'Contenido')
            return f"{category_short}: {tag_name}"
        else:
            category_map = {
                'cooking': 'Cooking',
                'tech': 'Tech',
                'general': 'Content',
                'home': 'Home',
                'business': 'Business',
                'travel': 'Travel',
                'entertainment': 'Entertainment',
                'education': 'Education'
            }
            category_short = category_map.get(category.lower(), 'Content')
            return f"{category_short}: {tag_name}"

    def _apply_spanish_corrections(self, tags: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Apply Spanish corrections - placeholder for future enhancements"""
        # This is a placeholder that can be enhanced with diacritic restoration
        # For now, it just returns the tags as-is since we handle corrections earlier
        return tags

    def _clean_tag_name(self, tag_name: str) -> str:
        """Clean and normalize tag names"""
        if not tag_name:
            return ""

        # Remove extra whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', tag_name.strip())

        # Remove special characters but keep accented characters
        cleaned = re.sub(r'[^\w\sáéíóúüñÁÉÍÓÚÜÑ]', '', cleaned)

        # Remove very short words
        if len(cleaned) < 2:
            return ""

        return cleaned

    def _is_valid_keyword(self, keyword: str, language: str = "english") -> bool:
        """Check if keyword is valid for tagging"""
        if not keyword or len(keyword) < 2:
            return False

        # Check if it's mostly alphabetic (allow some numbers)
        alpha_ratio = sum(c.isalpha() for c in keyword) / len(keyword)
        if alpha_ratio < 0.7:
            return False

        # Check against stopwords
        stopwords = self._get_stopwords(language)
        if keyword.lower() in stopwords:
            return False

        # Check for boilerplate
        if self._is_boilerplate(keyword):
            return False

        return True

    def _fallback_tags(self, text: str, max_tags: int) -> List[Dict[str, str]]:
        """Enhanced fallback tag generation when AI models fail"""
        try:
            # Detect language
            language = self.language_processor.detect_language(text)

            # Extract meaningful keywords using frequency analysis
            keywords = self._extract_meaningful_keywords_improved(text, language == "spanish", "general")

            # Convert to tag format
            tags = []
            for keyword, score in keywords[:max_tags]:
                if self._is_valid_keyword(keyword, language):
                    tag_name = self._format_phrase(keyword)
                    if tag_name and len(tag_name) >= 3:
                        tags.append({
                            'name': tag_name,
                            'color': random.choice(self.colors),
                            'description': self._generate_smart_description(tag_name, "general", language)
                        })

            return tags[:max_tags]

        except Exception as e:
            print(f"Error in fallback tag generation: {e}")
            # Ultimate fallback - simple word extraction
            words = re.findall(r'[A-Za-zÁÉÍÓÚÜÑáéíóúüñ]{4,}', text)
            unique_words = list(set(words))[:max_tags]

            return [{
                'name': word.capitalize(),
                'color': random.choice(self.colors),
                'description': f"Content: {word.capitalize()}"
            } for word in unique_words]


# Module-level convenience function
_default_generator = None

def generate_tags(text: str, title: str = "", max_tags: int = 10) -> List[Dict[str, str]]:
    """
    Module-level convenience function for tag generation.

    Args:
        text: The main text content to analyze
        title: Optional title for additional context
        max_tags: Maximum number of tags to generate

    Returns:
        List of tag dictionaries with 'name', 'color', and 'description' keys
    """
    global _default_generator
    if _default_generator is None:
        _default_generator = TagGenerator()

    # Handle async call properly
    import asyncio
    try:
        # Try to get the current event loop
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we need to use a different approach
        # For now, fall back to synchronous generation
        return _default_generator._generate_tags_sync(text, title, max_tags)
    except RuntimeError:
        # No event loop running, create a new one
        return asyncio.run(_default_generator.generate_tags(text, title=title, max_tags=max_tags))
