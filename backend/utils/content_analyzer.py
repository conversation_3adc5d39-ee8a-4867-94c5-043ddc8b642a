"""
Advanced content analysis for video tag generation.
Focuses on extracting meaningful content-based tags while filtering out technical metadata.
"""

import re
from typing import List, Dict, Set, Tuple, Optional
from collections import Counter


class ContentAnalyzer:
    """Analyzes video content to extract meaningful, searchable tags"""
    
    def __init__(self):
        self.spanish_food_ingredients = self._get_spanish_food_ingredients()
        self.spanish_cooking_techniques = self._get_spanish_cooking_techniques()
        self.spanish_dish_types = self._get_spanish_dish_types()
        self.spanish_cuisine_types = self._get_spanish_cuisine_types()
        self.spanish_spelling_corrections = self._get_spanish_spelling_corrections()
        self.technical_filters = self._get_technical_filters()
        
    def _get_spanish_food_ingredients(self) -> Set[str]:
        """Common Spanish food ingredients"""
        return {
            # Vegetables
            'tomate', 'tomates', 'cebolla', 'cebollas', 'ajo', 'ajos', 'pimiento', 'pimientos',
            'morrón', 'morrones', 'zanahoria', 'zanahorias', 'papa', 'papas', 'patata', 'patatas',
            'calabaza', 'calabacín', 'berenjena', 'berenjenas', 'espinaca', 'espinacas',
            'lechuga', 'apio', 'perejil', 'cilantro', 'albahaca', 'orégano', 'romero',
            
            # Proteins
            'pollo', 'carne', 'cerdo', 'ternera', 'pescado', 'salmón', 'atún', 'bacalao',
            'camarones', 'langostinos', 'mejillones', 'almejas', 'huevo', 'huevos',
            
            # Dairy
            'queso', 'quesos', 'leche', 'crema', 'mantequilla', 'yogur', 'nata',
            'mozzarella', 'parmesano', 'ricotta', 'queso fresco',
            
            # Grains & Legumes
            'arroz', 'pasta', 'fideos', 'macarrones', 'espaguetis', 'lentejas', 'garbanzos',
            'frijoles', 'alubias', 'quinoa', 'avena', 'trigo', 'harina', 'maicena',
            
            # Spices & Seasonings
            'pimienta', 'sal', 'azúcar', 'canela', 'comino', 'pimentón', 'paprika',
            'azafrán', 'nuez moscada', 'jengibre', 'curry', 'chile', 'ají',
            
            # Oils & Vinegars
            'aceite', 'oliva', 'vinagre', 'limón', 'lima',
            
            # Fruits
            'manzana', 'pera', 'plátano', 'naranja', 'fresa', 'uva', 'piña', 'mango',
            
            # Nuts & Seeds
            'almendra', 'nuez', 'piñón', 'avellana', 'sésamo'
        }
    
    def _get_spanish_cooking_techniques(self) -> Set[str]:
        """Spanish cooking techniques and methods"""
        return {
            'freír', 'frito', 'fritos', 'hornear', 'horneado', 'asar', 'asado', 'asados',
            'hervir', 'hervido', 'cocer', 'cocido', 'cocinar', 'guisar', 'guisado',
            'saltear', 'salteado', 'sofreír', 'sofrito', 'gratinar', 'gratinado',
            'marinar', 'marinado', 'adobar', 'adobado', 'empanar', 'empanado',
            'rebozar', 'rebozado', 'batir', 'mezclar', 'amasar', 'rallar', 'picar',
            'cortar', 'trocear', 'laminar', 'filetear', 'deshuesar', 'pelar',
            'condimentar', 'sazonar', 'aliñar', 'marcar', 'sellar', 'dorar',
            'reducir', 'espesar', 'ligar', 'montar', 'incorporar', 'envolver'
        }
    
    def _get_spanish_dish_types(self) -> Set[str]:
        """Types of Spanish dishes"""
        return {
            'paella', 'gazpacho', 'tortilla', 'croquetas', 'empanadas', 'tapas',
            'pinchos', 'bocadillo', 'sandwich', 'ensalada', 'sopa', 'crema',
            'guiso', 'estofado', 'cocido', 'fabada', 'migas', 'churros',
            'flan', 'natillas', 'arroz con leche', 'sangría', 'horchata',
            'merienda', 'desayuno', 'almuerzo', 'cena', 'aperitivo', 'postre',
            'entrada', 'plato principal', 'acompañamiento', 'guarnición',
            'bolitas', 'albóndigas', 'buñuelos', 'fritos', 'rebozados'
        }
    
    def _get_spanish_cuisine_types(self) -> Set[str]:
        """Types of Spanish cuisine"""
        return {
            'mediterránea', 'casera', 'tradicional', 'moderna', 'fusión',
            'vegetariana', 'vegana', 'sin gluten', 'saludable', 'rápida',
            'fácil', 'sencilla', 'económica', 'gourmet', 'familiar',
            'andaluza', 'catalana', 'vasca', 'gallega', 'valenciana',
            'madrileña', 'canaria', 'balear', 'asturiana', 'extremeña'
        }
    
    def _get_spanish_spelling_corrections(self) -> Dict[str, str]:
        """Common Spanish spelling corrections for cooking terms"""
        return {
            'pivienta': 'pimienta',
            'pimenta': 'pimienta',
            'pimiento': 'pimienta',  # Context-dependent
            'tomate': 'tomate',
            'sevolla': 'cebolla',
            'cevolla': 'cebolla',
            'queso': 'queso',
            'keso': 'queso',
            'ajo': 'ajo',
            'axo': 'ajo',
            'aceite': 'aceite',
            'azeite': 'aceite',
            'sal': 'sal',
            'azucar': 'azúcar',
            'azukar': 'azúcar',
            'canela': 'canela',
            'kanela': 'canela',
            'oregano': 'orégano',
            'oregano': 'orégano',
            'limon': 'limón',
            'vinagre': 'vinagre',
            'binagre': 'vinagre',
            'maicena': 'maicena',
            'maizena': 'maicena',
            'harina': 'harina',
            'arina': 'harina'
        }
    
    def _get_technical_filters(self) -> Set[str]:
        """Technical terms and metadata to filter out"""
        return {
            'snaptik', 'tiktok', 'instagram', 'facebook', 'youtube', 'app',
            'video', 'canal', 'suscribir', 'like', 'compartir', 'seguir',
            'download', 'descargar', 'link', 'enlace', 'url', 'www',
            'http', 'https', 'com', 'org', 'net', 'mp4', 'avi', 'mov',
            'watermark', 'marca de agua', 'logo', 'copyright', '©', '®',
            'subscribe', 'follow', 'share', 'comment', 'comentar',
            'notification', 'notificación', 'bell', 'campana'
        }
    
    def correct_spanish_spelling(self, word: str) -> str:
        """Correct common Spanish spelling errors"""
        word_lower = word.lower()
        if word_lower in self.spanish_spelling_corrections:
            corrected = self.spanish_spelling_corrections[word_lower]
            # Preserve original capitalization
            if word.isupper():
                return corrected.upper()
            elif word.istitle():
                return corrected.title()
            else:
                return corrected
        return word
    
    def is_technical_metadata(self, word: str) -> bool:
        """Check if word is technical metadata that should be filtered"""
        word_lower = word.lower()
        
        # Check against technical filters
        if word_lower in self.technical_filters:
            return True
        
        # Check for app/platform patterns
        if any(pattern in word_lower for pattern in ['app_', '.app', 'tik', 'snap', 'insta']):
            return True
        
        # Check for numeric IDs
        if re.match(r'^\d{10,}$', word):
            return True
        
        # Check for file extensions
        if re.match(r'.*\.(mp4|avi|mov|jpg|png|gif)$', word_lower):
            return True
        
        return False
    
    def extract_food_content_tags(self, text: str, max_tags: int = 8) -> List[Dict[str, str]]:
        """Extract food/cooking specific tags from content"""
        text_lower = text.lower()
        words = re.findall(r'\b[a-záéíóúñ]{3,}\b', text_lower)
        
        # Collect food-related terms
        ingredients = []
        techniques = []
        dishes = []
        cuisine_types = []
        
        for word in words:
            # Skip technical metadata
            if self.is_technical_metadata(word):
                continue
            
            # Correct spelling
            corrected_word = self.correct_spanish_spelling(word)
            
            # Categorize food terms
            if corrected_word in self.spanish_food_ingredients:
                ingredients.append(corrected_word)
            elif corrected_word in self.spanish_cooking_techniques:
                techniques.append(corrected_word)
            elif corrected_word in self.spanish_dish_types:
                dishes.append(corrected_word)
            elif corrected_word in self.spanish_cuisine_types:
                cuisine_types.append(corrected_word)
        
        # Count frequencies
        all_terms = ingredients + techniques + dishes + cuisine_types
        term_counts = Counter(all_terms)
        
        # Prioritize by category and frequency
        tags = []
        
        # Add most frequent ingredients (up to 4)
        ingredient_counts = Counter(ingredients)
        for ingredient, count in ingredient_counts.most_common(4):
            if len(tags) < max_tags:
                tags.append({
                    'name': ingredient.title(),
                    'category': 'ingredient',
                    'frequency': count
                })
        
        # Add dish types (up to 2)
        dish_counts = Counter(dishes)
        for dish, count in dish_counts.most_common(2):
            if len(tags) < max_tags:
                tags.append({
                    'name': dish.title(),
                    'category': 'dish',
                    'frequency': count
                })
        
        # Add cooking techniques (up to 2)
        technique_counts = Counter(techniques)
        for technique, count in technique_counts.most_common(2):
            if len(tags) < max_tags:
                tags.append({
                    'name': technique.title(),
                    'category': 'technique',
                    'frequency': count
                })
        
        # Add cuisine types (up to 1)
        cuisine_counts = Counter(cuisine_types)
        for cuisine, count in cuisine_counts.most_common(1):
            if len(tags) < max_tags:
                tags.append({
                    'name': cuisine.title(),
                    'category': 'cuisine',
                    'frequency': count
                })
        
        return tags
    
    def analyze_content_type(self, text: str) -> str:
        """Analyze what type of content this is"""
        text_lower = text.lower()
        
        # Food/cooking indicators
        food_indicators = ['cocinar', 'receta', 'ingredientes', 'hornear', 'freír', 'mezclar', 'queso', 'carne', 'pollo']
        food_score = sum(1 for indicator in food_indicators if indicator in text_lower)
        
        if food_score >= 3:
            return 'cooking'
        
        # Add other content type detection here
        return 'general'
    
    def extract_meaningful_tags(self, text: str, title: str = "", max_tags: int = 5) -> List[Dict[str, str]]:
        """Extract meaningful, content-based tags"""
        # Combine text sources but filter title for technical metadata
        clean_title = self._clean_title(title)
        full_text = f"{clean_title} {text}" if clean_title else text
        
        # Determine content type
        content_type = self.analyze_content_type(full_text)
        
        # Extract tags based on content type
        if content_type == 'cooking':
            # For cooking content, allow more tags (up to 8)
            cooking_max = min(max_tags + 3, 8) if max_tags == 5 else max_tags
            return self.extract_food_content_tags(full_text, cooking_max)
        else:
            # For other content, use general extraction
            return self._extract_general_tags(full_text, max_tags)
    
    def _clean_title(self, title: str) -> str:
        """Clean title by removing technical metadata"""
        if not title:
            return ""
        
        # Remove app names and technical identifiers
        title = re.sub(r'snaptik\.app_\d+', '', title, flags=re.IGNORECASE)
        title = re.sub(r'tiktok_\d+', '', title, flags=re.IGNORECASE)
        title = re.sub(r'instagram_\d+', '', title, flags=re.IGNORECASE)
        title = re.sub(r'\d{10,}', '', title)  # Remove long numeric IDs
        title = re.sub(r'[_\-]+', ' ', title)  # Replace underscores/dashes with spaces
        
        # Remove file extensions
        title = re.sub(r'\.(mp4|avi|mov|jpg|png|gif)$', '', title, flags=re.IGNORECASE)
        
        return title.strip()
    
    def _extract_general_tags(self, text: str, max_tags: int) -> List[Dict[str, str]]:
        """Extract general tags for non-cooking content"""
        # This is a simplified version - can be expanded for other content types
        words = re.findall(r'\b[a-záéíóúñ]{4,}\b', text.lower())
        
        # Filter out technical metadata and common words
        meaningful_words = []
        for word in words:
            if not self.is_technical_metadata(word):
                corrected = self.correct_spanish_spelling(word)
                meaningful_words.append(corrected)
        
        # Count and return most frequent
        word_counts = Counter(meaningful_words)
        tags = []
        
        for word, count in word_counts.most_common(max_tags):
            if len(word) >= 4:  # Minimum length for general tags
                tags.append({
                    'name': word.title(),
                    'category': 'general',
                    'frequency': count
                })
        
        return tags


class SpanishGrammarValidator:
    """Validates and corrects Spanish grammar and spelling for tags"""

    def __init__(self):
        self.common_corrections = self._get_common_corrections()
        self.valid_spanish_words = self._get_valid_spanish_words()
        self.accent_corrections = self._get_accent_corrections()

    def _get_common_corrections(self) -> Dict[str, str]:
        """Extended Spanish spelling corrections"""
        return {
            # Cooking terms
            'pivienta': 'pimienta',
            'pimenta': 'pimienta',
            'pimiento': 'pimienta',  # Context: when referring to spice
            'sevolla': 'cebolla',
            'cevolla': 'cebolla',
            'sebolla': 'cebolla',
            'keso': 'queso',
            'quesso': 'queso',
            'axo': 'ajo',
            'azeite': 'aceite',
            'azukar': 'azúcar',
            'azucar': 'azúcar',
            'kanela': 'canela',
            'oregano': 'orégano',
            'limon': 'limón',
            'binagre': 'vinagre',
            'maizena': 'maicena',
            'arina': 'harina',

            # Common words
            'receta': 'receta',
            'reseta': 'receta',
            'cocina': 'cocina',
            'kosina': 'cocina',
            'comida': 'comida',
            'komida': 'comida',
            'facil': 'fácil',
            'rapido': 'rápido',
            'delicioso': 'delicioso',
            'delisioso': 'delicioso',
            'sabroso': 'sabroso',

            # Techniques
            'freir': 'freír',
            'hornear': 'hornear',
            'ornear': 'hornear',
            'mezclar': 'mezclar',
            'meslar': 'mezclar',
            'batir': 'batir',
            'vatir': 'batir',
            'cortar': 'cortar',
            'kortar': 'cortar',

            # Dish types
            'entrada': 'entrada',
            'entradas': 'entradas',
            'merienda': 'merienda',
            'merenda': 'merienda',
            'desayuno': 'desayuno',
            'desalluno': 'desayuno',
            'almuerzo': 'almuerzo',
            'almuerso': 'almuerzo',
            'cena': 'cena',
            'sena': 'cena'
        }

    def _get_valid_spanish_words(self) -> Set[str]:
        """Set of valid Spanish words for validation"""
        return {
            # Ingredients
            'tomate', 'cebolla', 'ajo', 'pimienta', 'sal', 'azúcar', 'aceite',
            'queso', 'leche', 'huevo', 'harina', 'maicena', 'pollo', 'carne',
            'pescado', 'arroz', 'pasta', 'papa', 'patata', 'zanahoria',
            'pimiento', 'morrón', 'calabaza', 'espinaca', 'lechuga', 'apio',
            'perejil', 'cilantro', 'albahaca', 'orégano', 'canela', 'comino',
            'limón', 'vinagre', 'mantequilla', 'crema', 'yogur',

            # Techniques
            'freír', 'hornear', 'asar', 'hervir', 'cocer', 'cocinar', 'guisar',
            'saltear', 'sofreír', 'gratinar', 'marinar', 'empanar', 'rebozar',
            'batir', 'mezclar', 'amasar', 'rallar', 'picar', 'cortar', 'pelar',
            'condimentar', 'sazonar', 'dorar', 'reducir', 'espesar',

            # Dishes
            'paella', 'gazpacho', 'tortilla', 'croquetas', 'empanadas', 'tapas',
            'ensalada', 'sopa', 'crema', 'guiso', 'estofado', 'cocido',
            'merienda', 'desayuno', 'almuerzo', 'cena', 'entrada', 'postre',
            'bolitas', 'albóndigas', 'buñuelos', 'fritos',

            # Cuisine types
            'mediterránea', 'casera', 'tradicional', 'vegetariana', 'vegana',
            'saludable', 'rápida', 'fácil', 'sencilla', 'económica', 'familiar',

            # Descriptors
            'delicioso', 'sabroso', 'rico', 'exquisito', 'nutritivo', 'fresco',
            'caliente', 'frío', 'dulce', 'salado', 'picante', 'suave'
        }

    def _get_accent_corrections(self) -> Dict[str, str]:
        """Common accent corrections"""
        return {
            'facil': 'fácil',
            'rapido': 'rápido',
            'clasico': 'clásico',
            'tipico': 'típico',
            'basico': 'básico',
            'practico': 'práctico',
            'economico': 'económico',
            'organico': 'orgánico',
            'mediterranea': 'mediterránea',
            'oregano': 'orégano',
            'limon': 'limón',
            'azucar': 'azúcar',
            'freir': 'freír',
            'reir': 'reír'
        }

    def correct_word(self, word: str) -> str:
        """Correct a single word's spelling and grammar"""
        if not word:
            return word

        word_lower = word.lower()

        # Check common corrections first
        if word_lower in self.common_corrections:
            corrected = self.common_corrections[word_lower]
        # Check accent corrections
        elif word_lower in self.accent_corrections:
            corrected = self.accent_corrections[word_lower]
        else:
            corrected = word

        # Preserve original capitalization
        if word.isupper():
            return corrected.upper()
        elif word.istitle():
            return corrected.title()
        else:
            return corrected

    def validate_word(self, word: str) -> bool:
        """Check if a word is valid Spanish"""
        word_lower = word.lower()

        # Check if it's in our valid words list
        if word_lower in self.valid_spanish_words:
            return True

        # Check if it's a correctable word
        if word_lower in self.common_corrections:
            return True

        if word_lower in self.accent_corrections:
            return True

        # Basic Spanish word pattern validation
        if re.match(r'^[a-záéíóúñü]+$', word_lower):
            return True

        return False

    def correct_and_validate_tag(self, tag: str) -> Optional[str]:
        """Correct and validate a tag, return None if invalid"""
        if not tag or len(tag) < 3:
            return None

        # Correct the word
        corrected = self.correct_word(tag)

        # Validate the corrected word
        if self.validate_word(corrected):
            return corrected

        return None
