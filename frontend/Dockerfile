# Multi-stage build for production
FROM node:18-alpine as build

# Set working directory
WORKDIR /app

# Accept build arguments for environment variables
ARG REACT_APP_API_URL
ARG REACT_APP_BACKEND_EXTERNAL_PORT=8080
ARG REACT_APP_FRONTEND_EXTERNAL_PORT=3001
ARG REACT_APP_NGINX_PORT=8790

# Set environment variables for the build
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_BACKEND_EXTERNAL_PORT=$REACT_APP_BACKEND_EXTERNAL_PORT
ENV REACT_APP_FRONTEND_EXTERNAL_PORT=$REACT_APP_FRONTEND_EXTERNAL_PORT
ENV REACT_APP_NGINX_PORT=$REACT_APP_NGINX_PORT

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy all source code
COPY . .

# Debug: Show what was copied
RUN echo "=== DEBUG: Files in /app ===" && \
    ls -la && \
    echo "=== DEBUG: Looking for public directory ===" && \
    find . -name "public" -type d && \
    echo "=== DEBUG: Looking for index.html ===" && \
    find . -name "index.html" && \
    echo "=== DEBUG: Contents of public directory (if exists) ===" && \
    ls -la public/ 2>/dev/null || echo "Public directory not found!"

# If public directory doesn't exist, create it with a basic index.html
RUN if [ ! -d "public" ]; then \
        echo "Creating missing public directory..." && \
        mkdir -p public && \
        echo '<!DOCTYPE html><html><head><title>tagTok</title></head><body><div id="root"></div></body></html>' > public/index.html && \
        echo "Created basic index.html"; \
    fi

# Build for production (this eliminates all WebSocket issues)
RUN npm run build

# Production stage with nginx
FROM nginx:alpine

# Copy built app from build stage
COPY --from=build /app/build /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
