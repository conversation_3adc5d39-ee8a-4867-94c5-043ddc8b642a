import React, { useState, useEffect } from 'react';
import { PlayIcon } from '@heroicons/react/24/outline';
import { videoApi } from '../utils/api';

interface ThumbnailImageProps {
  thumbnailPath?: string;
  alt: string;
  className?: string;
  fallbackIcon?: React.ReactNode;
}

const ThumbnailImage: React.FC<ThumbnailImageProps> = ({
  thumbnailPath,
  alt,
  className = "w-full h-full object-cover",
  fallbackIcon
}) => {
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (!thumbnailPath) {
      setThumbnailUrl(null);
      return;
    }

    const loadThumbnail = async () => {
      setIsLoading(true);
      setHasError(false);
      
      try {
        const filename = thumbnailPath.split('/').pop() || '';
        const url = await videoApi.getThumbnailUrl(filename);
        setThumbnailUrl(url);
      } catch (error) {
        console.error('Failed to load thumbnail:', error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadThumbnail();

    // Cleanup function to revoke blob URL
    return () => {
      if (thumbnailUrl) {
        URL.revokeObjectURL(thumbnailUrl);
      }
    };
  }, [thumbnailPath]);

  // Cleanup blob URL when component unmounts
  useEffect(() => {
    return () => {
      if (thumbnailUrl) {
        URL.revokeObjectURL(thumbnailUrl);
      }
    };
  }, [thumbnailUrl]);

  if (!thumbnailPath || hasError) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-600">
        {fallbackIcon || <PlayIcon className="h-12 w-12 text-gray-400 dark:text-gray-300" />}
      </div>
    );
  }

  if (isLoading || !thumbnailUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700 animate-pulse">
        <div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <img
      src={thumbnailUrl}
      alt={alt}
      className={className}
      onError={() => {
        setHasError(true);
        if (thumbnailUrl) {
          URL.revokeObjectURL(thumbnailUrl);
        }
      }}
    />
  );
};

export default ThumbnailImage;
