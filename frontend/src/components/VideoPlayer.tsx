import React, { useRef, useEffect, useState } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

import { Video } from '../types';
import { videoApi } from '../utils/api';
import { useVideoSource } from './VideoSource';
import ThumbnailImage from './ThumbnailImage';

interface VideoPlayerProps {
  video: Video;
  className?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ video, className = '' }) => {
  const videoRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<any>(null);
  const [useHtml5, setUseHtml5] = useState(false);
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);

  // Use the authenticated video source hook
  const { videoUrl, isLoading: videoLoading, error: videoError } = useVideoSource(video.filename);

  // Handle window resize for responsive video sizing
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Add custom CSS for video player with optimized sizing
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      /* Video.js container - let it size naturally */
      .video-js:not(.vjs-fullscreen) {
        width: 100% !important;
        height: 100% !important;
      }

      .video-js:not(.vjs-fullscreen) .vjs-tech {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain;
      }

      /* Fullscreen mode styles */
      .video-js.vjs-fullscreen .vjs-tech {
        object-fit: contain !important;
      }

      .video-js.vjs-fullscreen video {
        object-fit: contain !important;
      }

      /* Ensure controls are visible and properly positioned */
      .video-js .vjs-control-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
      }

      /* Big play button centering is handled in global CSS */

      /* Fluid mode adjustments */
      .vjs-fluid {
        padding-top: 0 !important;
      }

      /* Video container responsive behavior */
      .video-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
      }

      /* Mobile optimizations */
      @media (max-width: 768px) {
        .video-container > div {
          max-width: 95vw !important;
          max-height: 70vh !important;
        }
      }

      @media (max-width: 480px) {
        .video-container > div {
          max-width: 100vw !important;
          max-height: 60vh !important;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Load thumbnail URL
  useEffect(() => {
    if (video.thumbnail_path) {
      const loadThumbnail = async () => {
        try {
          const filename = video.thumbnail_path?.split('/').pop() || '';
          const url = await videoApi.getThumbnailUrl(filename);
          setThumbnailUrl(url);
        } catch (error) {
          console.error('Failed to load thumbnail for video player:', error);
        }
      };
      loadThumbnail();
    }
  }, [video.thumbnail_path]);

  useEffect(() => {
    // Only initialize player when we have the video URL
    if (!playerRef.current && videoUrl) {
      const videoElement = document.createElement('video-js');

      videoElement.classList.add('vjs-big-play-centered');
      videoElement.style.width = '100%';
      videoElement.style.height = '100%';
      videoRef.current?.appendChild(videoElement);

      const player = playerRef.current = videojs(videoElement, {
        autoplay: false,
        controls: true,
        responsive: true,
        fluid: true, // Enable fluid mode for better responsive behavior
        fill: false, // Don't fill the container, maintain aspect ratio
        preload: 'auto',
        playbackRates: [0.5, 1, 1.25, 1.5, 2],
        aspectRatio: video.width && video.height ? `${video.width}:${video.height}` : '16:9',
        sources: [{
          src: videoUrl,
          type: 'video/mp4'
        }],
        poster: thumbnailUrl || undefined,
        html5: {
          vhs: {
            overrideNative: true
          }
        }
      }, () => {
        console.log('Video.js player initialized');
        // Ensure the player is properly sized
        player.ready(() => {
          console.log('Player ready, triggering resize');
          player.trigger('resize');
          // Try to load the video
          player.load();
        });
      });

      // Handle player errors
      player.on('error', () => {
        const error = player.error();
        console.error('Video player error:', error);
        console.log('Falling back to HTML5 video player');
        setUseHtml5(true);
      });

      // Handle fullscreen events to ensure proper aspect ratio
      player.on('fullscreenchange', () => {
        if (player.isFullscreen()) {
          console.log('Entered fullscreen mode');
          // Ensure aspect ratio preservation in fullscreen
          const videoEl = player.el().querySelector('video') as HTMLVideoElement;
          const techEl = player.el().querySelector('.vjs-tech') as HTMLElement;

          if (videoEl) {
            videoEl.style.objectFit = 'contain';
          }

          if (techEl) {
            techEl.style.objectFit = 'contain';
          }
        } else {
          console.log('Exited fullscreen mode');
          // Trigger resize to ensure proper sizing when exiting fullscreen
          setTimeout(() => {
            player.trigger('resize');
          }, 100);
        }
      });

      // Handle resize events
      player.on('resize', () => {
        console.log('Player resized');
        const videoEl = player.el().querySelector('video') as HTMLVideoElement;
        if (videoEl && !player.isFullscreen()) {
          videoEl.style.objectFit = 'contain';
        }
      });
    }
  }, [video, videoUrl, thumbnailUrl]);

  // Dispose the Video.js player when the functional component unmounts
  useEffect(() => {
    const player = playerRef.current;

    return () => {
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  // Calculate optimal dimensions to minimize black borders
  const getVideoDimensions = () => {
    if (!video.width || !video.height) return {
      containerStyle: { maxWidth: '800px', aspectRatio: '16/9' },
      videoStyle: { objectFit: 'contain' as const }
    };

    const aspectRatio = video.width / video.height;
    const isVertical = aspectRatio < 1;
    const isSquare = Math.abs(aspectRatio - 1) < 0.1;
    const isMobile = windowSize.width < 768;

    // Calculate optimal dimensions based on current viewport
    const availableWidth = isMobile ? windowSize.width * 0.95 : Math.min(windowSize.width * 0.8, 1000);
    const availableHeight = windowSize.height * (isMobile ? 0.6 : 0.7);

    if (isVertical) {
      // For vertical videos, prioritize height usage
      const optimalHeight = Math.min(availableHeight, 600);
      const optimalWidth = Math.min(optimalHeight * aspectRatio, availableWidth);

      return {
        containerStyle: {
          width: `${optimalWidth}px`,
          height: `${optimalHeight}px`,
          aspectRatio: `${video.width}/${video.height}`
        },
        videoStyle: { objectFit: 'contain' as const }
      };
    } else if (isSquare) {
      // For square videos, use balanced dimensions
      const optimalSize = Math.min(availableWidth, availableHeight, 500);
      return {
        containerStyle: {
          width: `${optimalSize}px`,
          height: `${optimalSize}px`,
          aspectRatio: '1/1'
        },
        videoStyle: { objectFit: 'contain' as const }
      };
    } else {
      // For horizontal videos, prioritize width usage
      const optimalWidth = Math.min(availableWidth, 900);
      const optimalHeight = Math.min(optimalWidth / aspectRatio, availableHeight);

      return {
        containerStyle: {
          width: `${optimalWidth}px`,
          height: `${optimalHeight}px`,
          aspectRatio: `${video.width}/${video.height}`
        },
        videoStyle: { objectFit: 'contain' as const }
      };
    }
  };

  const { containerStyle, videoStyle } = getVideoDimensions();

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden ${className}`}>
      <div className="video-container mx-auto pt-6 pb-2">
        <div
          className="relative bg-black rounded-lg overflow-hidden"
          style={{
            ...containerStyle,
            margin: '0 auto'
          }}
        >
          {useHtml5 ? (
            videoUrl ? (
              <video
                className="w-full h-full"
                controls
                preload="auto"
                poster={thumbnailUrl || undefined}
                style={{
                  ...videoStyle,
                  width: '100%',
                  height: '100%'
                }}
                onLoadedMetadata={(e) => {
                  // Ensure proper aspect ratio is maintained for HTML5 video
                  const videoElement = e.target as HTMLVideoElement;
                  if (videoElement) {
                    videoElement.style.objectFit = videoStyle.objectFit;
                  }
                }}
              >
                <source src={videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                {videoLoading ? (
                  <div className="text-center">
                    <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                    <p className="text-gray-600 dark:text-gray-300">Loading video...</p>
                  </div>
                ) : videoError ? (
                  <div className="text-center text-red-500">
                    <p>Failed to load video</p>
                    <p className="text-sm">{videoError.message}</p>
                  </div>
                ) : (
                  <p className="text-gray-600 dark:text-gray-300">Preparing video...</p>
                )}
              </div>
            )
          ) : (
            <div
              ref={videoRef}
              className="w-full h-full"
              style={{
                width: '100%',
                height: '100%'
              }}
            />
          )}
        </div>
      </div>

      {/* Video metadata below player */}
      <div className="px-4 pt-6 pb-4 border-t border-gray-200 dark:border-gray-600 mt-4">
        <h3 className="font-medium text-gray-900 dark:text-gray-100">{video.title}</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
          {video.original_filename}
        </p>
        {video.width && video.height && (
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
            {video.width} × {video.height}
          </p>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
