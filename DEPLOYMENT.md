# 🚀 tagTok Deployment Guide

This guide helps you deploy your own instance of tagTok for public use.

## 📋 Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- A domain name (optional for local deployment)
- Basic knowledge of environment variables

## 🔧 Quick Deployment

### 1. Clone and Configure

```bash
# Clone the repository
git clone https://github.com/yourusername/tagtok-multi-users.git
cd tagtok-multi-users

# Copy the production environment template
cp .env.production.example .env
```

### 2. Configure Your Deployment

Edit the `.env` file and change these **required** settings:

```bash
# Your domain (without https://)
PUBLIC_DOMAIN=yourdomain.com

# Set to production for live deployment
DEPLOYMENT_MODE=production

# Generate a secure secret key
SECRET_KEY=$(openssl rand -hex 32)

# Customize your app name
APP_NAME=MyTagTok
```

### 3. Deploy

```bash
# Start all services
docker-compose up -d

# Check that all services are healthy
docker-compose ps
```

Your tagTok instance will be available at:
- **Production**: `https://yourdomain.com` (with proper SSL setup)
- **Local**: `http://localhost:8790`

## ⚙️ Configuration Options

### Deployment Modes

| Mode | Description | Use Case |
|------|-------------|----------|
| `development` | Full logging, auto-reload, permissive CORS | Local development |
| `staging` | Production-like with extra logging | Testing before production |
| `production` | Optimized for performance and security | Live deployment |

### Feature Flags

```bash
# Control user registration
ENABLE_REGISTRATION=true

# Control public video sharing
ENABLE_PUBLIC_SHARING=true
```

### Database Options

```bash
# SQLite (default - good for small to medium deployments)
DATABASE_URL=sqlite:///db/tagTok.db

# PostgreSQL (recommended for production)
DATABASE_URL=postgresql://username:password@host:port/database
```

### AI Model Configuration

```bash
# Whisper model size affects accuracy vs performance
WHISPER_MODEL_SIZE=base  # tiny, base, small, medium, large
```

## 🌐 Domain Setup

### Option 1: Local Deployment
Set `PUBLIC_DOMAIN=localhost` for local-only access.

### Option 2: Public Domain
1. Set `PUBLIC_DOMAIN=yourdomain.com`
2. Point your domain's DNS to your server's IP
3. Set up SSL/TLS (see SSL Setup section)

## 🔒 SSL/HTTPS Setup

For production deployment with a public domain:

### Option 1: Reverse Proxy (Recommended)
Use nginx, Caddy, or Cloudflare to handle SSL termination:

```nginx
# nginx configuration example
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8790;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Option 2: Cloudflare (Easiest)
1. Add your domain to Cloudflare
2. Set DNS A record to point to your server
3. Enable "Flexible" or "Full" SSL in Cloudflare

## 🔧 Advanced Configuration

### Custom Branding
```bash
APP_NAME=MyCompanyVideos
APP_DESCRIPTION=Internal video organization platform
```

### Performance Tuning
```bash
# For high-traffic deployments
WHISPER_MODEL_SIZE=small  # Faster processing
ACCESS_TOKEN_EXPIRE_MINUTES=60  # Longer sessions
```

### Security Hardening
```bash
# Disable registration for private instances
ENABLE_REGISTRATION=false

# Disable public sharing for internal use
ENABLE_PUBLIC_SHARING=false
```

## 📊 Monitoring

Check service health:
```bash
# View service status
docker-compose ps

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Check API health
curl http://localhost:8790/api/health
```

## 🔄 Updates

To update your deployment:
```bash
# Pull latest changes
git pull

# Rebuild and restart services
docker-compose down
docker-compose up -d --build
```

## 🆘 Troubleshooting

### Common Issues

1. **502 Bad Gateway**: Backend service is down
   ```bash
   docker-compose logs backend
   docker-compose restart backend
   ```

2. **CORS Errors**: Check your `PUBLIC_DOMAIN` setting
   ```bash
   # Verify CORS configuration
   curl -H "Origin: https://yourdomain.com" http://localhost:8790/api/health
   ```

3. **Database Issues**: Check database permissions
   ```bash
   # Check database directory permissions
   ls -la data/db/
   ```

### Getting Help

1. Check the logs: `docker-compose logs`
2. Verify configuration: Visit `http://localhost:8790/api/` for config info
3. Open an issue on GitHub with your configuration (remove sensitive data)

## 📝 License

This project is open source. Feel free to modify and distribute according to the license terms.
