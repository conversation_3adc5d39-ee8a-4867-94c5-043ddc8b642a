#!/usr/bin/env python3
"""
Script to fix stuck videos by updating their processing status.
"""

import sqlite3
import os

DB_PATH = "data/db/tagTok.db"

def fix_stuck_videos():
    """Fix videos stuck in processing status"""
    print("🔧 Fixing stuck videos...")
    
    if not os.path.exists(DB_PATH):
        print(f"❌ Database not found at {DB_PATH}")
        return False
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Find stuck videos
        cursor.execute("SELECT id, title, processing_status FROM videos WHERE processing_status = 'processing'")
        stuck_videos = cursor.fetchall()
        
        if not stuck_videos:
            print("✅ No stuck videos found")
            conn.close()
            return True
        
        print(f"Found {len(stuck_videos)} stuck video(s):")
        for video_id, title, status in stuck_videos:
            print(f"  - {title} (ID: {video_id}) - Status: {status}")
        
        # Update stuck videos to failed status
        cursor.execute("UPDATE videos SET processing_status = 'failed' WHERE processing_status = 'processing'")
        updated_count = cursor.rowcount
        conn.commit()
        
        print(f"✅ Updated {updated_count} videos from 'processing' to 'failed'")
        print("   You can now reprocess these videos if needed")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 TagTok Stuck Video Fix Tool")
    print("=" * 40)
    fix_stuck_videos()
