#!/usr/bin/env python3
"""
Project cleanup script to remove temporary test files, debug scripts, and redundant documentation.
This script will help clean up the TagTok project by removing files that are no longer needed.
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

# Files to remove (safe to delete)
FILES_TO_REMOVE = [
    # Test and verification scripts
    "test_api_isolation.py",
    "test_erase_all.py", 
    "test_tag_duplication_fix.py",
    "test_user_isolation.py",
    "verify_implementation.py",
    "verify_tag_fix.py",
    "manage_superuser.py",
    "fix_stuck_video.py",
    
    # Debug and troubleshooting scripts
    "debug_recipe_extraction.py",
    "reset_processing_status.py",
    "backend/reset_processing_status.py",
    
    # Migration scripts (already executed)
    "migrate_port_config.py",
    
    # Temporary documentation
    "TAG_DUPLICATION_FIX_SUMMARY.md",
    "USER_ISOLATION_IMPLEMENTATION_SUMMARY.md", 
    "test_sharing_features.md",
    "IMPLEMENTATION_SUMMARY.md",
    
    # Root package files (likely not needed)
    "package.json",
    "package-lock.json",
]

# Files to potentially remove (ask user)
OPTIONAL_FILES = [
    "DEPLOYMENT.md",  # If DEPLOYMENT_GUIDE.md covers the same content
]

def create_backup_zip(files_to_backup):
    """Create a ZIP backup of all files before deletion"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"tagtok_cleanup_backup_{timestamp}.zip"

    try:
        with zipfile.ZipFile(backup_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            backed_up_count = 0
            for file_path in files_to_backup:
                if os.path.exists(file_path):
                    # Add file to zip with its relative path
                    zipf.write(file_path, file_path)
                    backed_up_count += 1

        if backed_up_count > 0:
            backup_size = get_file_size(backup_filename)
            print(f"📦 Created backup ZIP: {backup_filename} ({backup_size})")
            print(f"   Contains {backed_up_count} files")
            return backup_filename
        else:
            # Remove empty zip file
            if os.path.exists(backup_filename):
                os.remove(backup_filename)
            return None

    except Exception as e:
        print(f"❌ Failed to create backup ZIP: {e}")
        return None

def remove_file(file_path):
    """Remove a file"""
    if not os.path.exists(file_path):
        return False, "File not found"

    try:
        os.remove(file_path)
        return True, "Removed successfully"
    except Exception as e:
        return False, f"Error: {e}"

def get_file_size(file_path):
    """Get file size in a human readable format"""
    if not os.path.exists(file_path):
        return "N/A"
    
    size = os.path.getsize(file_path)
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def analyze_files():
    """Analyze files to be removed and show summary"""
    print("🔍 Analyzing files to be removed...")
    print("=" * 60)
    
    total_size = 0
    existing_files = []
    missing_files = []
    
    for file_path in FILES_TO_REMOVE:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            existing_files.append((file_path, size))
            print(f"📄 {file_path} ({get_file_size(file_path)})")
        else:
            missing_files.append(file_path)
    
    print(f"\n📊 Summary:")
    print(f"  - Files to remove: {len(existing_files)}")
    print(f"  - Files already missing: {len(missing_files)}")
    print(f"  - Total size to free: {get_file_size_from_bytes(total_size)}")
    
    if missing_files:
        print(f"\n⚠️  Files already missing:")
        for file_path in missing_files:
            print(f"  - {file_path}")
    
    return existing_files

def get_file_size_from_bytes(size_bytes):
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def cleanup_files(create_backup_zip=False):
    """Remove the identified files"""
    print("\n🗑️  Starting cleanup...")
    print("=" * 60)

    # Create backup ZIP if requested
    backup_file = None
    if create_backup_zip:
        existing_files = [f for f in FILES_TO_REMOVE if os.path.exists(f)]
        if existing_files:
            backup_file = create_backup_zip(existing_files)
            if backup_file:
                print(f"✅ Backup created successfully")
            else:
                print(f"⚠️  Backup creation failed, proceeding without backup")
        else:
            print(f"⚠️  No files to backup")

    removed_count = 0
    failed_count = 0
    total_freed = 0

    for file_path in FILES_TO_REMOVE:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            success, message = remove_file(file_path)

            if success:
                print(f"✅ Removed: {file_path}")
                removed_count += 1
                total_freed += file_size
            else:
                print(f"❌ Failed: {file_path} - {message}")
                failed_count += 1
        else:
            print(f"⚠️  Already missing: {file_path}")

    print(f"\n📊 Cleanup Results:")
    print(f"  - Files removed: {removed_count}")
    print(f"  - Files failed: {failed_count}")
    print(f"  - Space freed: {get_file_size_from_bytes(total_freed)}")

    if backup_file:
        print(f"  - Backup saved: {backup_file}")

    return removed_count, failed_count, backup_file

def check_optional_files():
    """Check optional files and ask user"""
    print("\n🤔 Optional files to consider removing:")
    print("=" * 60)
    
    for file_path in OPTIONAL_FILES:
        if os.path.exists(file_path):
            size = get_file_size(file_path)
            print(f"📄 {file_path} ({size})")
            
            # Check if it's a duplicate
            if file_path == "DEPLOYMENT.md" and os.path.exists("DEPLOYMENT_GUIDE.md"):
                print("  ℹ️  Note: DEPLOYMENT_GUIDE.md also exists")
    
    return len([f for f in OPTIONAL_FILES if os.path.exists(f)])

def compress_existing_backups():
    """Compress existing .backup files into a single ZIP"""
    print("\n🗜️  Compressing existing backup files...")

    # Find all .backup files
    backup_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.backup'):
                backup_files.append(os.path.join(root, file))

    if not backup_files:
        print("⚠️  No .backup files found")
        return None

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"tagtok_existing_backups_{timestamp}.zip"

    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for backup_file in backup_files:
                # Add file to zip with relative path
                zipf.write(backup_file, backup_file)

        zip_size = get_file_size(zip_filename)
        print(f"📦 Created backup ZIP: {zip_filename} ({zip_size})")
        print(f"   Contains {len(backup_files)} backup files")

        # Ask if user wants to remove the individual backup files
        response = input(f"\n❓ Remove the {len(backup_files)} individual .backup files? (yes/no): ").lower().strip()

        if response == 'yes':
            removed_count = 0
            for backup_file in backup_files:
                try:
                    os.remove(backup_file)
                    removed_count += 1
                except Exception as e:
                    print(f"❌ Failed to remove {backup_file}: {e}")

            print(f"✅ Removed {removed_count} individual backup files")

        return zip_filename

    except Exception as e:
        print(f"❌ Failed to create backup ZIP: {e}")
        return None

def main():
    """Main cleanup function"""
    print("🧹 TagTok Project Cleanup Tool")
    print("=" * 60)
    print("This tool will remove temporary test files, debug scripts,")
    print("and redundant documentation that are no longer needed.")
    print()

    # Check if there are existing backup files to compress
    backup_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.backup'):
                backup_files.append(os.path.join(root, file))

    if backup_files:
        print(f"🔍 Found {len(backup_files)} existing .backup files")
        compress_response = input("❓ Compress existing backup files into a ZIP? (yes/no): ").lower().strip()

        if compress_response == 'yes':
            compress_existing_backups()
            print()  # Add spacing
    
    # Analyze files
    existing_files = analyze_files()
    
    if not existing_files:
        print("✅ No files to remove - project is already clean!")
        return
    
    # Check optional files
    optional_count = check_optional_files()
    
    # Ask user for confirmation
    print(f"\n❓ Do you want to proceed with cleanup?")
    print(f"   This will remove {len(existing_files)} files.")
    
    response = input("\nEnter 'yes' to proceed, 'backup' to create backups first, or 'no' to cancel: ").lower().strip()
    
    if response == 'yes':
        removed, failed, backup_file = cleanup_files(create_backup_zip=False)
        print(f"\n🎉 Cleanup completed!")
    elif response == 'backup':
        removed, failed, backup_file = cleanup_files(create_backup_zip=True)
        print(f"\n🎉 Cleanup completed with backup!")
    else:
        print("\n❌ Cleanup cancelled")
        return
    
    # Handle optional files
    if optional_count > 0:
        print(f"\n❓ Remove optional files too?")
        opt_response = input("Enter 'yes' to remove optional files: ").lower().strip()
        
        if opt_response == 'yes':
            for file_path in OPTIONAL_FILES:
                if os.path.exists(file_path):
                    success, message = remove_file(file_path)
                    if success:
                        print(f"✅ Removed optional file: {file_path}")
                    else:
                        print(f"❌ Failed to remove: {file_path} - {message}")
    
    print(f"\n✨ Project cleanup finished!")
    print(f"📁 Your TagTok project is now cleaner and more organized.")

def compress_backups_only():
    """Standalone function to just compress existing backup files"""
    print("🗜️  TagTok Backup Compression Tool")
    print("=" * 60)

    result = compress_existing_backups()

    if result:
        print(f"\n✨ Backup compression completed!")
        print(f"📁 All backup files are now in: {result}")
    else:
        print(f"\n⚠️  No backup files found or compression failed")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--compress-backups":
        compress_backups_only()
    else:
        main()
