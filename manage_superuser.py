#!/usr/bin/env python3
"""
Script to manage superuser status and check user permissions.
This script helps with:
1. Checking if users exist and their superuser status
2. Creating a superuser account
3. Promoting existing users to superuser
4. Testing superuser access to admin endpoints
"""

import requests
import sqlite3
import os
import sys
from datetime import datetime

BASE_URL = "http://localhost:8790/api"
DB_PATH = "data/db/tagTok.db"

def check_database_users():
    """Check users in the database directly"""
    print("🔍 Checking users in database...")
    
    if not os.path.exists(DB_PATH):
        print(f"❌ Database not found at {DB_PATH}")
        return []
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, email, is_active, is_superuser, created_at FROM users")
        users = cursor.fetchall()
        
        if not users:
            print("❌ No users found in database")
            return []
        
        print(f"✅ Found {len(users)} users:")
        for user in users:
            user_id, username, email, is_active, is_superuser, created_at = user
            status = "ACTIVE" if is_active else "INACTIVE"
            super_status = "SUPERUSER" if is_superuser else "REGULAR"
            print(f"  - {username} (ID: {user_id}) - {status} - {super_status}")
            print(f"    Email: {email}")
            print(f"    Created: {created_at}")
            print()
        
        conn.close()
        return users
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return []

def make_user_superuser(username):
    """Make a user a superuser by updating the database directly"""
    print(f"🔧 Making user '{username}' a superuser...")
    
    if not os.path.exists(DB_PATH):
        print(f"❌ Database not found at {DB_PATH}")
        return False
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if user exists
        cursor.execute("SELECT id, username, is_superuser FROM users WHERE username = ?", (username,))
        user = cursor.fetchone()
        
        if not user:
            print(f"❌ User '{username}' not found")
            conn.close()
            return False
        
        user_id, username, is_superuser = user
        
        if is_superuser:
            print(f"✅ User '{username}' is already a superuser")
            conn.close()
            return True
        
        # Update user to superuser
        cursor.execute("UPDATE users SET is_superuser = 1 WHERE username = ?", (username,))
        conn.commit()
        
        print(f"✅ Successfully made '{username}' a superuser")
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def create_superuser_account(username, password, email):
    """Create a new superuser account via API"""
    print(f"🔧 Creating superuser account '{username}'...")
    
    try:
        # First try to register the user
        register_data = {
            "username": username,
            "password": password,
            "email": email,
            "full_name": f"Superuser {username}"
        }
        
        response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
        
        if response.status_code == 200:
            print(f"✅ User '{username}' created successfully")
            # Now make them a superuser via database
            return make_user_superuser(username)
        else:
            print(f"❌ Failed to create user: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API error: {e}")
        return False

def test_superuser_access(username, password):
    """Test if a user has superuser access by trying to login and access admin endpoint"""
    print(f"🧪 Testing superuser access for '{username}'...")
    
    try:
        # Login
        login_data = {"username": username, "password": password}
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
        
        token_data = response.json()
        token = token_data["access_token"]
        user_info = token_data["user"]
        
        print(f"✅ Login successful for '{username}'")
        print(f"   User ID: {user_info['id']}")
        print(f"   Is Active: {user_info['is_active']}")
        print(f"   Is Superuser: {user_info['is_superuser']}")
        
        if not user_info['is_superuser']:
            print(f"❌ User '{username}' is not a superuser")
            return False
        
        # Test admin endpoint access
        headers = {"Authorization": f"Bearer {token}"}
        
        # Try to access a superuser-only endpoint (list users)
        response = requests.get(f"{BASE_URL}/auth/users", headers=headers)
        
        if response.status_code == 200:
            print(f"✅ Superuser access confirmed - can access admin endpoints")
            return True
        else:
            print(f"❌ Superuser access failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_erase_all_access(username, password):
    """Test access to the erase-all endpoint specifically"""
    print(f"🧪 Testing erase-all endpoint access for '{username}'...")
    
    try:
        # Login
        login_data = {"username": username, "password": password}
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            return False
        
        token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test the erase-all endpoint (but don't actually call it)
        # We'll just check if we get the right error or if it would work
        print("⚠️  Note: This is just testing access, not actually erasing data")
        
        # For safety, let's just test a different admin endpoint first
        response = requests.get(f"{BASE_URL}/auth/users", headers=headers)
        
        if response.status_code == 200:
            print(f"✅ Admin access confirmed - erase-all endpoint should work")
            print(f"🚨 WARNING: User '{username}' has access to erase-all endpoint")
            return True
        else:
            print(f"❌ Admin access denied: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main function to manage superuser access"""
    print("🔧 TagTok Superuser Management Tool")
    print("=" * 50)
    
    # Check existing users
    users = check_database_users()
    
    # Check if default admin exists
    admin_exists = any(user[1] == "admin" for user in users)
    admin_is_super = any(user[1] == "admin" and user[4] for user in users)
    
    if admin_exists and admin_is_super:
        print("✅ Default admin user exists and is a superuser")
        print("   Username: admin")
        print("   Password: admin123")
        print("\n🧪 Testing admin access...")
        if test_superuser_access("admin", "admin123"):
            print("\n✅ You can use the erase-all endpoint with:")
            print("   Username: admin")
            print("   Password: admin123")
            return
    
    # Check if acel user exists and can be made superuser
    acel_exists = any(user[1] == "acel" for user in users)
    if acel_exists:
        print("\n🔧 Making 'acel' user a superuser...")
        if make_user_superuser("acel"):
            print("✅ Testing acel superuser access...")
            if test_superuser_access("acel", "cacota08"):
                print("\n✅ You can now use the erase-all endpoint with:")
                print("   Username: acel")
                print("   Password: cacota08")
                return
    
    # Create a new superuser if needed
    print("\n🔧 Creating new superuser account...")
    if create_superuser_account("superadmin", "superadmin123", "<EMAIL>"):
        print("✅ Testing new superuser access...")
        if test_superuser_access("superadmin", "superadmin123"):
            print("\n✅ You can now use the erase-all endpoint with:")
            print("   Username: superadmin")
            print("   Password: superadmin123")
            return
    
    print("\n❌ Failed to create or configure superuser access")
    print("Manual steps:")
    print("1. Check if the application is running")
    print("2. Verify database permissions")
    print("3. Try running the migration script: python backend/migrations/001_add_user_system.py")

if __name__ == "__main__":
    main()
