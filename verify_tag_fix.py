#!/usr/bin/env python3
"""
Quick verification script to check the current state of video tags after the fix.
"""

import requests
import json

BASE_URL = "http://localhost:8790/api"

def login_user(username, password):
    """Login a user and return the access token"""
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": username,
        "password": password
    })
    
    if response.status_code == 200:
        data = response.json()
        return data["access_token"]
    else:
        print(f"❌ Failed to login {username}: {response.status_code}")
        return None

def get_video_info(video_id, token):
    """Get video information including tags"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/videos/{video_id}", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Failed to get video {video_id}: {response.status_code}")
        return None

def main():
    print("🔍 Verifying Tag Duplication Fix")
    print("=" * 40)
    
    # Login
    token = login_user("acel", "cacota08")
    if not token:
        return
    
    # Get videos
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/videos", headers=headers)
    
    if response.status_code != 200:
        print(f"❌ Failed to get videos: {response.status_code}")
        return
    
    videos = response.json()
    
    print(f"📊 Found {len(videos)} videos")
    print()
    
    for video in videos:
        video_id = video["id"]
        title = video["title"]
        status = video.get("processing_status", "unknown")
        tag_count = len(video.get("tags", []))
        
        print(f"🎥 Video: {title}")
        print(f"   ID: {video_id}")
        print(f"   Status: {status}")
        print(f"   Tag count: {tag_count}")
        
        if video.get("tags"):
            tag_names = [tag["name"] for tag in video["tags"]]
            print(f"   Tags: {tag_names}")
        else:
            print(f"   Tags: None")
        
        print()
    
    # Check if any video has excessive tags (indication of accumulation)
    max_tags = max(len(video.get("tags", [])) for video in videos) if videos else 0
    
    print("📊 Analysis:")
    if max_tags <= 5:
        print(f"✅ SUCCESS: Maximum tag count is {max_tags} (≤5)")
        print("✅ No tag accumulation detected!")
    elif max_tags <= 10:
        print(f"⚠️ MODERATE: Maximum tag count is {max_tags}")
        print("⚠️ Some accumulation may have occurred")
    else:
        print(f"❌ HIGH: Maximum tag count is {max_tags}")
        print("❌ Significant tag accumulation detected")
    
    print("\n🔧 Fix Status:")
    print("✅ Tag clearing logic implemented")
    print("✅ Reprocessing endpoint updated")
    print("✅ Tag assignment with deduplication added")
    print("✅ Intelligent merge option available")

if __name__ == "__main__":
    main()
