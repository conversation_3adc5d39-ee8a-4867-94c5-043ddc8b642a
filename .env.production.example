# tagTok Production Deployment Configuration
# Copy this file to .env and modify for your production deployment

# =============================================================================
# DEPLOYMENT CONFIGURATION - REQUIRED: Change these for your deployment
# =============================================================================

# Public domain where your app will be hosted (without protocol)
# Example: "mytagtok.com", "videos.mydomain.org"
PUBLIC_DOMAIN=yourdomain.com

# Deployment mode: production (for live deployment)
DEPLOYMENT_MODE=production

# Application branding (customize for your instance)
APP_NAME=MyTagTok
APP_DESCRIPTION=My TikTok video organization platform
APP_VERSION=1.0.0

# Feature flags
ENABLE_REGISTRATION=true
ENABLE_PUBLIC_SHARING=true

# =============================================================================
# SECURITY CONFIGURATION - REQUIRED: Generate secure values
# =============================================================================

# IMPORTANT: Generate a secure secret key for JWT tokens
# Use: openssl rand -hex 32
SECRET_KEY=CHANGE-THIS-TO-A-SECURE-RANDOM-STRING-32-CHARS

# JWT token expiration (in minutes)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# INFRASTRUCTURE CONFIGURATION
# =============================================================================

# Database Configuration
# For production, consider using PostgreSQL:
# DATABASE_URL=postgresql://username:password@host:port/database
DATABASE_URL=sqlite:///db/tagTok.db

# Directory Paths (usually don't need to change)
VIDEOS_DIR=/app/videos
TRANSCRIPTS_DIR=/app/transcripts

# Port Configuration - External ports (what users connect to)
NGINX_PORT=80
BACKEND_EXTERNAL_PORT=8080
FRONTEND_EXTERNAL_PORT=3001
OLLAMA_EXTERNAL_PORT=11435

# Internal service ports (inside Docker containers - usually don't change)
BACKEND_INTERNAL_PORT=8000
FRONTEND_INTERNAL_PORT=80
OLLAMA_INTERNAL_PORT=11434

# Service URLs for application code (usually don't need to change)
BACKEND_URL=http://backend:8000
OLLAMA_URL=http://ollama:11434
FRONTEND_URL=http://frontend:80

# API Configuration for frontend (auto-configured, leave empty)
REACT_APP_API_URL=

# CORS Configuration for backend (auto-configured, leave empty)
CORS_ORIGINS=

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================

# Whisper model size: tiny, base, small, medium, large
# Larger models are more accurate but require more resources
WHISPER_MODEL_SIZE=base

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Development Configuration
PYTHONPATH=/app
CHOKIDAR_USEPOLLING=true

# Docker Configuration
COMPOSE_PROJECT_NAME=tagtok

# yt-dlp Configuration (for downloading videos that require authentication)
# Path to cookies file (e.g., exported from browser)
# YTDLP_COOKIES_FILE=/app/cookies/cookies.txt
# Browser to extract cookies from (chrome, firefox, safari, etc.)
# YTDLP_COOKIES_FROM_BROWSER=chrome

# =============================================================================
# DEPLOYMENT NOTES
# =============================================================================
# 
# 1. Change PUBLIC_DOMAIN to your actual domain
# 2. Generate a secure SECRET_KEY using: openssl rand -hex 32
# 3. Set DEPLOYMENT_MODE=production
# 4. Customize APP_NAME and APP_DESCRIPTION for your instance
# 5. Consider using PostgreSQL for DATABASE_URL in production
# 6. Set up SSL/TLS termination (HTTPS) at your reverse proxy/load balancer
# 7. Configure your domain's DNS to point to your server
# 8. Set up proper backup procedures for your database and videos
#
