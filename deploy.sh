#!/bin/bash

# tagTok Deployment Script
# This script helps you deploy your own tagTok instance

set -e

echo "🚀 tagTok Deployment Script"
echo "=========================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Function to generate a secure secret key
generate_secret_key() {
    if command -v openssl &> /dev/null; then
        openssl rand -hex 32
    else
        # Fallback for systems without openssl
        cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 64 | head -n 1
    fi
}

# Check if .env already exists
if [ -f ".env" ]; then
    echo "⚠️  .env file already exists."
    read -p "Do you want to reconfigure? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Using existing configuration..."
        SKIP_CONFIG=true
    fi
fi

if [ "$SKIP_CONFIG" != "true" ]; then
    echo "📝 Let's configure your tagTok instance..."
    echo

    # Get deployment mode
    echo "Select deployment mode:"
    echo "1) Development (local testing)"
    echo "2) Production (public deployment)"
    read -p "Enter choice (1-2): " DEPLOY_MODE_CHOICE

    case $DEPLOY_MODE_CHOICE in
        1)
            DEPLOYMENT_MODE="development"
            PUBLIC_DOMAIN="localhost"
            ;;
        2)
            DEPLOYMENT_MODE="production"
            read -p "Enter your public domain (e.g., mytagtok.com): " PUBLIC_DOMAIN
            if [ -z "$PUBLIC_DOMAIN" ]; then
                echo "❌ Public domain is required for production deployment."
                exit 1
            fi
            ;;
        *)
            echo "❌ Invalid choice. Exiting."
            exit 1
            ;;
    esac

    # Get app name
    read -p "Enter your app name (default: tagTok): " APP_NAME
    APP_NAME=${APP_NAME:-tagTok}

    # Get app description
    read -p "Enter app description (default: TikTok video organization platform): " APP_DESCRIPTION
    APP_DESCRIPTION=${APP_DESCRIPTION:-"TikTok video organization platform"}

    # Ask about registration
    read -p "Enable user registration? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        ENABLE_REGISTRATION="false"
    else
        ENABLE_REGISTRATION="true"
    fi

    # Generate secret key
    echo "🔐 Generating secure secret key..."
    SECRET_KEY=$(generate_secret_key)

    # Create .env file
    echo "📄 Creating .env file..."
    cat > .env << EOF
# tagTok Environment Configuration
# Generated by deploy.sh on $(date)

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Public domain where your app will be hosted (without protocol)
PUBLIC_DOMAIN=$PUBLIC_DOMAIN

# Deployment mode: development, staging, production
DEPLOYMENT_MODE=$DEPLOYMENT_MODE

# Application branding
APP_NAME=$APP_NAME
APP_DESCRIPTION=$APP_DESCRIPTION
APP_VERSION=1.0.0

# Feature flags
ENABLE_REGISTRATION=$ENABLE_REGISTRATION
ENABLE_PUBLIC_SHARING=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT secret key (auto-generated)
SECRET_KEY=$SECRET_KEY
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# INFRASTRUCTURE CONFIGURATION
# =============================================================================

# Database Configuration
DATABASE_URL=sqlite:///db/tagTok.db

# Directory Paths
VIDEOS_DIR=/app/videos
TRANSCRIPTS_DIR=/app/transcripts

# Port Configuration - External ports (what users connect to)
NGINX_PORT=8790
BACKEND_EXTERNAL_PORT=8080
FRONTEND_EXTERNAL_PORT=3001
OLLAMA_EXTERNAL_PORT=11435

# Internal service ports (inside Docker containers)
BACKEND_INTERNAL_PORT=8000
FRONTEND_INTERNAL_PORT=80
OLLAMA_INTERNAL_PORT=11434

# Service URLs for application code
BACKEND_URL=http://backend:8000
OLLAMA_URL=http://ollama:11434
FRONTEND_URL=http://frontend:80

# API Configuration for frontend (auto-configured)
REACT_APP_API_URL=

# CORS Configuration for backend (auto-configured)
CORS_ORIGINS=

# AI Model Configuration
WHISPER_MODEL_SIZE=base

# Development Configuration
PYTHONPATH=/app
CHOKIDAR_USEPOLLING=true

# Docker Configuration
COMPOSE_PROJECT_NAME=tagtok

# yt-dlp Configuration
# YTDLP_COOKIES_FILE=/app/cookies/cookies.txt
# YTDLP_COOKIES_FROM_BROWSER=chrome
EOF

    echo "✅ Configuration saved to .env"
fi

# Deploy the application
echo "🐳 Starting Docker containers..."
docker-compose down 2>/dev/null || true
docker-compose up -d --build

echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."
if curl -f http://localhost:8790/api/health >/dev/null 2>&1; then
    echo "✅ Backend is healthy"
else
    echo "❌ Backend health check failed"
    echo "📋 Checking logs..."
    docker-compose logs backend --tail=10
    exit 1
fi

# Display success message
echo
echo "🎉 tagTok deployment successful!"
echo "================================"
echo
if [ "$DEPLOYMENT_MODE" = "production" ]; then
    echo "🌐 Your tagTok instance will be available at:"
    echo "   https://$PUBLIC_DOMAIN (after SSL setup)"
    echo "   http://$PUBLIC_DOMAIN (without SSL)"
    echo
    echo "⚠️  For production deployment, make sure to:"
    echo "   1. Set up SSL/TLS (HTTPS) using a reverse proxy"
    echo "   2. Point your domain's DNS to this server"
    echo "   3. Configure firewall rules"
    echo "   4. Set up regular backups"
else
    echo "🌐 Your tagTok instance is available at:"
    echo "   http://localhost:8790"
fi
echo
echo "📚 API Documentation: http://localhost:8790/api/docs"
echo "🔧 Configuration: .env file"
echo "📋 Logs: docker-compose logs"
echo
echo "🆘 Need help? Check DEPLOYMENT.md or open an issue on GitHub"
